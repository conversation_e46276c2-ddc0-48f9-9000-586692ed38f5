<template>
  <div class="notice-form">
    <div class="p-4">
      <!-- 表单内容 -->
      <div class="form-card">
        <div class="form-card-body">
          <a-form
            :model="formData"
            :rules="rules"
            ref="formRef"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
            layout="horizontal"
          >
            <a-row :gutter="20">
              <a-col :span="12">
                <a-form-item label="信息发布编码" name="code">
                  <a-input v-model:value="formData.code" placeholder="信息发布编码为只读项" disabled />
                  <div class="help-text">数据传到国资监管平台后将返回信息发布编码</div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="公告标题" name="name">
                  <a-input v-model:value="formData.name" placeholder="请输入公告标题" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="20">
              <a-col :span="12">
                <a-form-item label="所属集团" name="groupName">
                  <JDictSelectTag disabled v-model:value="formData.groupName" :showChooseOption="false" dictCode="group_name" placeholder="请选择" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="所属企业" name="companyName">
                  <ApiSelect
                    v-model:value="formData.companyName"
                    placeholder="请选择所属企业"
                    :api="getUserCompany"
                    :transResult="(originResult) => originResult.map((item) => ({ value: item.id, label: item.name }))"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="20">
              <a-col :span="12">
                <a-form-item label="管理单位" name="manageUnit">
                  <ApiSelect
                    v-model:value="formData.manageUnit"
                    placeholder="请选择管理单位"
                    :api="getCompanyHandle"
                    :transResult="(originResult) => originResult.map((item) => ({ value: item.id, label: item.name }))"
                  />
                  <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="是否报送国资委" name="reportOrNot">
                  <JDictSelectTag v-model:value="formData.reportOrNot" :showChooseOption="false" dictCode="yes_no" placeholder="请选择" />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="20">
              <a-col :span="12">
                <a-form-item label="经办人" name="operator">
                  <a-input v-model:value="formData.operator" placeholder="请输入经办人" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="录入人" name="entryClerk">
                  <a-input v-model:value="formData.entryClerk" placeholder="录入人" disabled />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="20">
              <a-col :span="12">
                <a-form-item label="录入时间" name="createTime">
                  <a-input v-model:value="formData.createTime" placeholder="录入时间" disabled />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="20">
              <a-col :span="12">
                <a-form-item label="招租方式" name="rentType">
                  <JDictSelectTag v-model:value="formData.rentType" :showChooseOption="false" dictCode="rent_type" placeholder="请选择招租方式" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="状态" name="status">
                  <a-select v-model:value="formData.status" placeholder="请选择状态">
                    <a-select-option value="0" :disabled="initStatus === '1' || initStatus === '2' || initStatus === '4'">草稿</a-select-option>
                    <a-select-option value="1" :disabled="initStatus === '2' || initStatus === '4'">备案</a-select-option>
                    <a-select-option value="2" :disabled="initStatus === '-1' || initStatus === '0' || initStatus === '4'">撤回</a-select-option>
                    <a-select-option value="4" :disabled="initStatus === '-1' || initStatus === '0' || initStatus === '1'">作废</a-select-option>
                  </a-select>
                  <div class="help-text">备案数据支持撤回、草稿数据和撤回数据支持作废</div>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="20">
              <a-col :span="12">
                <a-form-item label="公示开始时间" name="publicStartTime">
                  <a-date-picker
                    v-model:value="formData.publicStartTime"
                    show-time
                    placeholder="请选择公示开始时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="公示结束时间" name="publicEndTime">
                  <a-date-picker
                    v-model:value="formData.publicEndTime"
                    show-time
                    placeholder="请选择公示结束时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item label="内容描述" name="contentDescription" :label-col="{ span: 3 }">
              <JEditor v-model:value="formData.contentDescription" placeholder="请输入内容描述" />
            </a-form-item>

            <a-form-item label="附件" name="files" :label-col="{ span: 3 }">
              <JUpload
                v-model:value="formData.files"
                :maxSize="50"
                :maxNumber="10"
                :multiple="true"
                :download="true"
                :removeConfirm="true"
                :returnUrl="false"
                :accept="['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.png', '.jpeg', '.jpg']"
                helpMessage="文件格式为.doc、.docx、.xls、.xlsx、.pdf、.png、.jpeg、.jpg，单个文件最大不超过50M，可以上传多个附件。"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- 表单操作按钮 -->
      <div class="form-footer">
        <a-button type="primary" @click="handleSubmit" :loading="loading"> 提交 </a-button>
        <a-button @click="handleReset" style="margin-left: 12px"> 重置 </a-button>
        <a-button @click="handleBack" style="margin-left: 12px"> 返回 </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="NoticeFormPage" setup>
  import { ref, onMounted, computed, reactive } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { JAreaLinkage, JDictSelectTag, ApiSelect } from '/@/components/Form';
  import { JUpload } from '/@/components/Form/src/jeecg/components/JUpload';
  import { addNotice, updateNotice, getNoticeDetail } from './notice.api';
  import { getUserCompany, getCompanyHandle } from '/@/api/common/api';
  import dayjs from 'dayjs';
  import { useUserStore } from '/@/store/modules/user';
  import JEditor from '/@/components/Form/src/jeecg/components/JEditor.vue';

  const route = useRoute();
  const router = useRouter();
  const userStore = useUserStore();
  const { createMessage } = useMessage();
  const loading = ref(false);
  const formRef = ref();

  // 获取路由参数判断是新增还是编辑
  const isUpdate = ref(false);
  const recordId = ref('');

  const initStatus = ref('-1');

  // 表单数据
  const formData = reactive({
    code: '',
    name: '',
    groupName: '0',
    companyName: '',
    manageUnit: '',
    reportOrNot: '',
    operator: '',
    entryClerk: userStore.getUserInfo.realname,
    createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    rentType: null,
    status: '',
    publicStartTime: '',
    publicEndTime: '',
    contentDescription: '',
    files: [],
  });

  // 表单验证规则
  const rules = {
    name: [{ required: true, message: '请输入公告标题', trigger: 'blur' }],
    companyName: [{ required: true, message: '请选择所属企业', trigger: 'change' }],
    manageUnit: [{ required: true, message: '请选择管理单位', trigger: 'change' }],
    reportOrNot: [{ required: true, message: '请选择是否报送国资委', trigger: 'change' }],
    operator: [{ required: true, message: '请输入经办人', trigger: 'blur' }],
    rentType: [{ required: true, message: '请选择招租方式', trigger: 'change' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],
    publicStartTime: [{ required: true, message: '请选择公示开始时间', trigger: 'change' }],
    publicEndTime: [{ required: true, message: '请选择公示结束时间', trigger: 'change' }],
    contentDescription: [{ required: true, message: '请输入内容描述', trigger: 'blur' }],
  };

  // 初始化
  onMounted(async () => {
    // 判断是新增还是编辑
    if (route.params?.id) {
      isUpdate.value = true;
      recordId.value = route.params.id as string;
      await loadData();
    } else {
      isUpdate.value = false;
      setDefaultValues();
    }
  });

  // 设置默认值
  function setDefaultValues() {
    Object.assign(formData, {
      groupName: '0',
      reportOrNot: '',
      entryClerk: userStore.getUserInfo.realname,
      createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      status: '',
    });
  }

  // 加载数据
  async function loadData() {
    try {
      const record = await getNoticeDetail(recordId.value);
      console.log(record, 'record');
      const data = record?.notice || {};
      if (data.files) {
        if (!Array.isArray(data.files)) {
          data.files = [{ url: data.files, name: '附件' }];
        }
      }
      Object.assign(formData, {
        ...data,
        groupName: `${data.groupName}`,
        rentType: `${data.rentType}`,
        reportOrNot: `${data.reportOrNot}`,
        status: `${data.status}`,
        // companyName: data.companyName ? `${data.companyName}`.split(',') : [],
        publicStartTime: data.publicStartTime ? dayjs(data.publicStartTime).format('YYYY-MM-DD HH:mm:ss') : '',
        publicEndTime: data.publicEndTime ? dayjs(data.publicEndTime).format('YYYY-MM-DD HH:mm:ss') : '',
        files: data.files || [],
      });
      initStatus.value = `${data.status}`;
      console.log(formData, 'formData');
    } catch (error) {
      createMessage.error('加载数据失败');
    }
  }

  // 重置表单
  function handleReset() {
    formRef.value?.resetFields();
    if (!isUpdate.value) {
      setDefaultValues();
    }
  }

  // 提交表单
  async function handleSubmit() {
    try {
      loading.value = true;
      await formRef.value?.validate();

      const submitData = { ...formData, companyName: Array.isArray(formData.companyName) ? formData.companyName.join(',') : formData.companyName };
      if (isUpdate.value) {
        submitData.id = recordId.value;
      }

      if (isUpdate.value) {
        await updateNotice(submitData);
        createMessage.success('编辑成功');
      } else {
        await addNotice(submitData);
        createMessage.success('新增成功');
      }

      router.back();
    } catch (error) {
      console.error('提交失败:', error);
      createMessage.error('提交失败');
    } finally {
      loading.value = false;
    }
  }

  // 返回
  function handleBack() {
    router.back();
  }
</script>

<style lang="less" scoped>
  .notice-form {
    .simple-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
      padding: 16px 24px;
      background: #fff;
      border-bottom: 1px solid #f0f0f0;
    }

    .form-card {
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      margin-bottom: 16px;

      .form-card-body {
        padding: 24px;
      }
    }

    .form-footer {
      text-align: center;
      padding: 16px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .help-text {
      font-size: 12px;
      color: #999;
      margin-top: 4px;
    }
  }
</style> 
