import { BasicColumn } from '/@/components/Table';
import { h } from 'vue';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { getTextByCode } from '/@/components/Form/src/utils/areaDataUtil';
import { getCompanyHandle, getUserCompany } from '/@/api/common/api';

export const columns: BasicColumn[] = [
  {
    title: '资产编号',
    dataIndex: 'code',
    width: 120,
    fixed: 'left',
  },
  {
    title: '企业自定义编号',
    dataIndex: 'enterpriseCode',
    width: 150,
  },
  {
    title: '资产名称',
    dataIndex: 'name',
    width: 160,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => {
      return render.renderDict(text, 'record_status');
    },
  },
  {
    title: '所属集团',
    dataIndex: 'groupName',
    width: 160,
    customRender: ({ text }) => {
      return render.renderDict(text, 'group_name');
    },
  },
  {
    title: '所属企业',
    dataIndex: 'companyNameString',
    width: 160,
  },
  {
    title: '管理单位',
    dataIndex: 'manageUnitName',
    width: 160,
  },
  {
    title: '省份',
    dataIndex: 'province',
    width: 100,
    customRender: ({ text }) => {
      return getTextByCode(text);
    },
  },
  {
    title: '城市',
    dataIndex: 'city',
    width: 100,
    customRender: ({ text }) => {
      return getTextByCode(text);
    },
  },
  {
    title: '区县',
    dataIndex: 'area',
    width: 100,
    customRender: ({ text }) => {
      return getTextByCode(text);
    },
  },
  {
    title: '详细地址',
    dataIndex: 'address',
    width: 200,
  },
  {
    title: '是否报送国资委',
    dataIndex: 'reportOrNot',
    width: 150,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '广告位面积(㎡)',
    dataIndex: 'adArea',
    width: 140,
    align: 'right',
  },
  {
    title: '资产使用状态',
    dataIndex: 'assetsStatus',
    width: 150,
    customRender: ({ text }) => {
      const textArray = text ? text.split(',') : [];
      return h(
        'div',
        textArray.map((item) => {
          const finalText = render.renderDict(item, 'land_assets_status', true);
          return h('span', finalText);
        })
      );
    },
  },
  {
    title: '设置时间',
    dataIndex: 'setDate',
    width: 120,
  },
  {
    title: '资产原值(元)',
    dataIndex: 'assetsAmount',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '账面价值(元)',
    dataIndex: 'bookAmount',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '账面价值时点',
    dataIndex: 'dateOfBookValue',
    width: 150,
  },
  {
    title: '剩余可使用年限',
    dataIndex: 'remainingUsefulYears',
    width: 150,
  },
  {
    title: '广告位数量',
    dataIndex: 'adQuantity',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(0) : '';
    },
  },
  {
    title: '是否审批',
    dataIndex: 'approvalStatus',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '是否入账',
    dataIndex: 'entryStatus',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '广告位管理部门',
    dataIndex: 'adOrg',
    width: 160,
  },
  {
    title: '广告位联系人',
    dataIndex: 'adContacts',
    width: 120,
  },
  {
    title: '联系电话',
    dataIndex: 'contactsPhone',
    width: 120,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
  },
  {
    title: '经办人',
    dataIndex: 'operator',
    width: 120,
  },
  {
    title: '录入人',
    dataIndex: 'entryClerk',
    width: 120,
  },
  {
    title: '录入时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 150,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '资产名称',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入资产名称',
    },
  },
  {
    field: 'code',
    label: '资产编号',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入资产编号',
    },
  },
  {
    field: 'companyName',
    label: '所属企业',
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择所属企业',
      api: getUserCompany,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择状态',
      dictCode: 'record_status',
    },
  },
  {
    field: 'enterpriseCode',
    label: '企业自定义编号',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入企业自定义编号',
    },
  },
  {
    field: 'manageUnit',
    label: '管理单位',
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择管理单位',
      api: getCompanyHandle,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    field: 'assetsLocation',
    label: '资产位置',
    component: 'JAreaLinkage',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择省/市/区',
    },
  },
  {
    field: 'address',
    label: '详细地址',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入详细地址',
    },
  },
  {
    field: 'reportOrNot',
    label: '是否报送国资委',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    field: 'operator',
    label: '经办人',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    field: 'entryClerk',
    label: '录入人',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入录入人',
    },
  },
  {
    field: 'createTime',
    label: '录入时间',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'assetsStatus',
    label: '资产使用状态',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择资产使用状态',
      mode: 'multiple',
      dictCode: 'land_assets_status',
    },
  },
  {
    field: 'adArea',
    label: '广告位面积',
    component: 'JRangeNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '最小值',
      precision: 2,
      min: 0,
    },
  },
  // {
  //   field: 'adAreaMax',
  //   label: '广告位面积最大值',
  //   component: 'InputNumber',
  //   colProps: { span: 6 },
  //   componentProps: {
  //     placeholder: '最大值',
  //     precision: 2,
  //     min: 0,
  //   },
  // },
  {
    field: 'approvalStatus',
    label: '是否审批',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择是否审批',
      ictCode: 'yes_no',
    },
  },
  {
    field: 'entryStatus',
    label: '是否入账',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择是否入账',
      ictCode: 'yes_no',
    },
  },
];
