import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { getCompanyHandle, getUserCompany } from '/@/api/common/api';

export const columns: BasicColumn[] = [
  {
    title: '租赁资产包编号',
    dataIndex: 'code',
    width: 160,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '租赁资产包名称',
    dataIndex: 'name',
    width: 180,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '出租方式',
    dataIndex: 'rentType',
    width: 200,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'rental_type');
    },
  },
  {
    title: '专业化招商',
    dataIndex: 'merchantsType',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '管理单位',
    dataIndex: 'manageUnit',
    width: 220,
  },
  {
    title: '报送国资委',
    dataIndex: 'reportOrNot',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '挂牌价格',
    dataIndex: 'listingPrice',
    width: 120,
    align: 'right',
  },
  {
    title: '价格单位',
    dataIndex: 'priceUnit',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'price_unit');
    },
  },
  {
    title: '租期',
    dataIndex: 'lease',
    width: 120,
  },
  {
    title: '招租面积(㎡)',
    dataIndex: 'rentArea',
    width: 140,
    align: 'right',
  },
  {
    title: '挂牌机构',
    dataIndex: 'listingOrg',
    width: 220,
  },
  {
    title: '出租方名称',
    dataIndex: 'lessorName',
    width: 220,
  },
  {
    title: '出租方类型',
    dataIndex: 'lessorType',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'lessor_type');
    },
  },
  {
    title: '经济类型',
    dataIndex: 'economicType',
    width: 160,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'economic_type');
    },
  },
  {
    title: '挂牌（公示）开始时间',
    dataIndex: 'listingStartDate',
    width: 160,
  },
  {
    title: '挂牌（公示）截止时间',
    dataIndex: 'listingEndDate',
    width: 160,
  },
  {
    title: '经办人',
    dataIndex: 'operator',
    width: 120,
  },
  {
    title: '录入人',
    dataIndex: 'entryClerk',
    width: 120,
  },
  {
    title: '录入时间',
    dataIndex: 'createTime',
    width: 160,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'record_status');
    },
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '资产包编号',
    field: 'code',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产包编号',
    },
  },
  {
    label: '资产包名称',
    field: 'name',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产包名称',
    },
  },
  {
    label: '出租方式',
    field: 'rentType',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择出租方式',
      dictCode: 'rental_type',
    },
  },
  {
    label: '状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择状态',
      dictCode: 'record_status',
    },
  },
  {
    label: '管理单位',
    field: 'manageUnit',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择管理单位',
      api: getCompanyHandle,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    label: '出租方名称',
    field: 'lessorName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入出租方名称',
    },
  },
  {
    label: '专业化招商',
    field: 'merchantsType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'yes_no',
      placeholder: '请选择',
    },
  },
  {
    label: '经办人',
    field: 'operator',
    component: 'Input',
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    label: '录入人',
    field: 'entryClerk',
    component: 'Input',
    componentProps: {
      placeholder: '请输入录入人',
    },
  },
  {
    label: '资产类型',
    field: 'assetTypes',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择资产类型',
      mode: 'multiple',
      dictCode: 'assets_type',
    },
  },
  {
    label: '租期',
    field: 'lease',
    component: 'Input',
    componentProps: {
      placeholder: '请输入租期',
    },
  },
  {
    label: '承租方',
    field: 'dealName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入承租方',
    },
  },
  {
    label: '是否报送国资委',
    field: 'reportOrNot',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    field: 'priceRange',
    label: '挂牌价格区间',
    component: 'JRangeNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入挂牌价格区间',
      precision: 2,
      min: 0,
    },
  },
  {
    field: 'areaRange',
    label: '招租面积区间',
    component: 'JRangeNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入挂牌价格区间',
      precision: 2,
      min: 0,
    },
  },
  // {
  //   label: '招租面积区间',
  //   field: 'minArea',
  //   component: 'InputNumber',
  //   componentProps: {
  //     placeholder: '最小值',
  //     min: 0,
  //     precision: 2,
  //     style: { width: '100%' },
  //   },
  // },
  // {
  //   label: '招租面积区间',
  //   field: 'maxArea',
  //   component: 'InputNumber',
  //   componentProps: {
  //     placeholder: '最大值',
  //     min: 0,
  //     precision: 2,
  //     style: { width: '100%' },
  //   },
  // },
  {
    label: '挂牌（公示）时间',
    field: 'listingDate',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '录入时间',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
];
