<template>
  <div class="rent-form">
    <div class="p-4">
      <a-form :model="formData" :rules="rules" ref="formRef" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }"
        layout="horizontal">
        <!-- 资产包信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:box-outlined" class="title-icon" />
              资产包信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="租赁资产包编号" name="code">
                  <a-input v-model:value="formData.code" placeholder="保存后系统自动生成" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="租赁资产包名称" name="name">
                  <a-input v-model:value="formData.name" placeholder="请输入租赁资产包名称" />
                  <div class="help-text">系统内要求资产名称唯一</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="出租方式" name="rentType">
                  <JDictSelectTag
                    v-model:value="formData.rentType"
                    :showChooseOption="false"
                    placeholder="请选择出租方式"
                    :disabled="isEditMode"
                    dictCode="rental_type"
                    @change="handleRentTypeChange"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="是否专业化招商" name="merchantsType">
                  <JDictSelectTag v-model:value="formData.merchantsType" :showChooseOption="false" placeholder="是否专业化招商" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item
                  label="关联公开招租"
                  name="relatedPublicLeasing"
                  :rules="formData.rentType === '4' ? [{ required: true, message: '请选择关联公开招租' }] : []"
                >
                  <a-select
                    v-model:value="formData.relatedPublicLeasing"
                    placeholder="请选择关联公开招租"
                    :disabled="formData.rentType !== '4'"
                    show-search
                    :filter-option="false"
                    :not-found-content="loading ? '加载中...' : null"
                    @search="handleRelatedPublicLeasingSearch"
                    @focus="() => handleRelatedPublicLeasingSearch('')"
                  >
                    <a-select-option v-for="item in relatedPublicLeasingOptions" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="管理单位" name="manageUnit">
                  <a-select v-model:value="formData.manageUnit" placeholder="请选择管理单位">
                    <a-select-option v-for="item in enterpriseOptions" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                  <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="是否报送国资委" name="reportOrNot">
                  <a-select v-model:value="formData.reportOrNot" placeholder="请选择">
                    <a-select-option :value="0">否</a-select-option>
                    <a-select-option :value="1">是</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="经办人" name="operator">
                  <a-input v-model:value="formData.operator" placeholder="请输入经办人" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="录入人" name="entryClerk">
                  <a-input v-model:value="formData.entryClerk" placeholder="录入人" disabled />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="录入时间" name="createTime">
                  <a-input v-model:value="formData.createTime" placeholder="录入时间" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="状态" name="status">
                  <a-select v-model:value="formData.status" placeholder="请选择状态">
                    <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value"
                      :disabled="item.disabled">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                  <div class="help-text">备案数据支持撤回、草稿数据和撤回数据支持作废</div>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="备注" name="remark">
                  <a-input v-model:value="formData.remark" placeholder="请输入备注" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 挂牌信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:tag-outlined" class="title-icon" />
              挂牌信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="挂牌价格" name="listingPrice">
                  <a-input-number v-model:value="formData.listingPrice" :precision="2" :controls="false"
                    style="width: 100%" placeholder="请输入挂牌价格" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="价格单位" name="priceUnit">
                  <a-select v-model:value="formData.priceUnit">
                    <a-select-option :value="0">元/月</a-select-option>
                    <a-select-option :value="1">元/年</a-select-option>
                    <a-select-option :value="2">元/平方米/年</a-select-option>
                    <a-select-option :value="3">元/平方米/月</a-select-option>
                    <a-select-option :value="4">一口价</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="租期" name="lease">
                  <a-input v-model:value="formData.lease" placeholder="请输入租期" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="招租面积（㎡）" name="rentArea">
                  <a-input-number v-model:value="formData.rentArea" :precision="2" :controls="false" style="width: 100%"
                    placeholder="请输入招租面积" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="挂牌机构" name="listingOrg"
                  :rules="formData.rentType === '1' ? [{ required: true, message: '请输入挂牌机构' }] : []">
                  <a-input v-model:value="formData.listingOrg" placeholder="请输入挂牌机构"
                    :disabled="formData.rentType !== '1'" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="挂牌机构所在地区" name="listingLocation"
                  :rules="formData.rentType === '1' ? [{ required: true, message: '请选择挂牌机构所在地区' }] : []">
                  <a-cascader v-model:value="formData.listingLocation" :options="locationOptions" placeholder="请选择省份/城市"
                    :disabled="formData.rentType !== '1'" allow-clear />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 出租方信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:bank-outlined" class="title-icon" />
              出租方信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="出租方名称" name="lessorName">
                  <a-select v-model:value="formData.lessorName" placeholder="请输入出租方名称搜索" show-search
                    :filter-option="false" :not-found-content="loading ? '加载中...' : null" @search="handleLessorSearch"
                    @focus="() => handleLessorSearch('')" @change="handleLessorChange">
                    <a-select-option v-for="item in lessorOptions" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="出租方类型" name="lessorType">
                  <a-select v-model:value="formData.lessorType">
                    <a-select-option :value="0">法人</a-select-option>
                    <a-select-option :value="1">其他组织</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="所在地区" name="address">
                  <a-input v-model:value="formData.address" placeholder="请输入所在地区" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="注册地地址" name="registration">
                  <a-input v-model:value="formData.registration" placeholder="请输入注册地地址" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="注册资本(万元)" name="registeredCapital">
                  <a-input-number v-model:value="formData.registeredCapital" :precision="2" :controls="false"
                    style="width: 100%" placeholder="请输入注册资本" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="经济类型" name="economicType">
                  <a-select v-model:value="formData.economicType">
                    <a-select-option :value="0">国有独资企业</a-select-option>
                    <a-select-option :value="1">国有全资企业</a-select-option>
                    <a-select-option :value="2">国有控股企业</a-select-option>
                    <a-select-option :value="3">国有实际控制企业</a-select-option>
                    <a-select-option :value="4">国有参股企业</a-select-option>
                    <a-select-option :value="5">事业单位</a-select-option>
                    <a-select-option :value="6">其他</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="法定代表人" name="legalRepresentative">
                  <a-input v-model:value="formData.legalRepresentative" placeholder="请输入法定代表人" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="所属行业" name="industry">
                  <a-input v-model:value="formData.industry" placeholder="请输入所属行业" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 披露信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
              披露信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="挂牌（公示）开始时间" name="listingStartDate"
                  :rules="['0', '1', '2'].includes(formData.rentType) ? [{ required: true, message: '请选择挂牌（公示）开始时间' }] : []">
                  <a-date-picker v-model:value="formData.listingStartDate" placeholder="选择日期" format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="挂牌（公示）截止时间" name="listingEndDate"
                  :rules="['0', '1', '2'].includes(formData.rentType) ? [{ required: true, message: '请选择挂牌（公示）截止时间' }] : []">
                  <a-date-picker v-model:value="formData.listingEndDate" placeholder="选择日期" format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD" style="width: 100%" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item label="内容描述" :label-col="{ span: 3 }" :wrapper-col="{ span: 16 }"
                  name="contentDescription">
                  <a-textarea v-model:value="formData.contentDescription" :rows="5" placeholder="请输入内容描述" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item label="附件" :label-col="{ span: 3 }" :wrapper-col="{ span: 16 }" name="fileList">
                  <JUpload v-model:value="formData.fileList" :bizPath="'rent'" :returnUrl="false" :multiple="true"
                    :removeConfirm="true"
                    helpMessage="文件格式为.doc、.docx、.xls、.xlsx、.pdf、.png、.jpeg、.jpg，单个文件最大不超过50M，可以上传多个附件。" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 关联资产部分 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:link-outlined" class="title-icon" />
              关联资产
            </div>
            <div class="form-card-action">
              <a-button type="primary" size="small" @click="addAssociatedAsset">
                <Icon icon="ant-design:plus-outlined" />
                新增关联资产
              </a-button>
            </div>
          </div>
          <div class="form-card-body">
            <a-table :dataSource="formData.associatedAssets" :columns="associatedAssetsColumns" :pagination="false"
              size="small" :bordered="true" rowKey="uid" :scroll="{ x: 800 }">
              <!-- 资产类型列 -->
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.dataIndex === 'assetType'">
                  <a-form-item :name="['associatedAssets', index, 'assetType']"
                    :rules="{ required: true, message: '请选择资产类型' }" class="mb-0">
                    <a-select v-model:value="record.assetType" size="small" style="width: 100%"
                      @change="() => handleAssetTypeChange(record)">
                      <a-select-option :value="0">土地</a-select-option>
                      <a-select-option :value="1">房屋</a-select-option>
                      <a-select-option :value="2">设备</a-select-option>
                      <a-select-option :value="3">广告位</a-select-option>
                      <a-select-option :value="4">其他</a-select-option>
                    </a-select>
                  </a-form-item>
                </template>

                <!-- 资产名称列 -->
                <template v-else-if="column.dataIndex === 'assetsCode'">
                  <a-form-item :name="['associatedAssets', index, 'assetsCode']"
                    :rules="{ required: true, message: '请选择资产' }" class="mb-0">
                    <a-select v-model:value="record.assetsCode" placeholder="请选择资产" size="small" style="width: 100%"
                      show-search :filter-option="false" :not-found-content="loading ? '加载中...' : null"
                      @search="handleAssetSearch" @focus="() => handleAssetSearch('')"
                      @change="(val) => handleAssetChange(val, index)">
                      <a-select-option v-for="item in assetOptions" :key="item.value" :value="item.value">
                        {{ item.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </template>

                <!-- 标的名称列 -->
                <template v-else-if="column.dataIndex === 'targetName'">
                  <a-form-item :name="['associatedAssets', index, 'targetName']"
                    :rules="{ required: true, message: '请输入标的名称' }" class="mb-0">
                    <a-input v-model:value="record.targetName" size="small" />
                  </a-form-item>
                </template>

                <!-- 标的所占面积列 -->
                <template v-else-if="column.dataIndex === 'area'">
                  <a-form-item :name="['associatedAssets', index, 'area']" class="mb-0">
                    <a-input-number v-model:value="record.area" :precision="2" :controls="false" size="small"
                      style="width: 100%" />
                  </a-form-item>
                </template>

                <!-- 操作列 -->
                <template v-else-if="column.dataIndex === 'action'">
                  <a-button type="primary" danger size="small" @click="removeAssociatedAsset(index)">
                    <Icon icon="ant-design:delete-outlined" />
                  </a-button>
                </template>
              </template>
            </a-table>

            <div v-if="formData.associatedAssets.length === 0" class="empty-hint">
              暂无关联资产，请点击上方"新增关联资产"按钮添加。
            </div>
          </div>
        </div>

        <!-- 成交信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:handshake-outlined" class="title-icon" />
              成交信息
            </div>
            <div class="form-card-action">
              <a-button type="primary" size="small" @click="addTransaction">
                <Icon icon="ant-design:plus-outlined" />
                新增成交信息
              </a-button>
            </div>
          </div>
          <div class="form-card-body">
            <div v-if="formData.transactions.length > 0">
              <div v-for="(transaction, index) in formData.transactions" :key="index" class="transaction-item">
                <div class="transaction-item-header">
                  <h4>成交信息 #{{ index + 1 }}</h4>
                  <a-button type="primary" danger size="small" @click="removeTransaction(index)">
                    删除
                  </a-button>
                </div>
                <div class="transaction-item-body">
                  <a-row :gutter="20">
                    <a-col :span="8">
                      <a-form-item label="标的名称" :name="['transactions', index, 'targetName']"
                        :rules="{ required: true, message: '请选择标的名称' }">
                        <a-select v-model:value="transaction.targetName" placeholder="请选择"
                          @change="() => handleTransactionTargetChange(transaction)">
                          <a-select-option v-for="asset in formData.associatedAssets" :key="asset.targetName"
                            :value="asset.targetName">
                            {{ asset.targetName }}
                          </a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="是否成交" :name="['transactions', index, 'dealStatus']"
                        :rules="{ required: true, message: '请选择是否成交' }">
                        <a-radio-group v-model:value="transaction.dealStatus"
                          @change="() => handleDealStatusChange(index)">
                          <a-radio :value="0">否</a-radio>
                          <a-radio :value="1">是</a-radio>
                        </a-radio-group>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="标的面积(m²)" :name="['transactions', index, 'targetArea']" :rules="[
                        {
                          validator: (rule, value, callback) =>
                            validateTargetArea(rule, value, callback, transaction),
                          trigger: 'blur',
                        },
                      ]">
                        <a-input-number v-model:value="transaction.targetArea" :precision="2" :controls="false"
                          style="width: 100%"
                          :disabled="transaction.dealStatus !== 1 || transaction.isTargetAreaDisabled" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="20">
                    <a-col :span="8">
                      <a-form-item label="承租方" :name="['transactions', index, 'dealName']" :rules="[
                        {
                          required: transaction.dealStatus === 1,
                          message: '请输入承租方',
                          trigger: 'blur',
                        },
                      ]">
                        <a-input v-model:value="transaction.dealName" placeholder="请输入承租方"
                          :disabled="transaction.dealStatus !== 1" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="成交价格" :name="['transactions', index, 'dealPrice']" :rules="[
                        {
                          required: transaction.dealStatus === 1,
                          message: '请输入成交价格',
                          trigger: 'blur',
                        },
                      ]">
                        <a-input-number v-model:value="transaction.dealPrice" :precision="2" :controls="false"
                          style="width: 100%" :disabled="transaction.dealStatus !== 1" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="价格单位" :name="['transactions', index, 'dealUnit']" :rules="[
                        {
                          required: transaction.dealStatus === 1,
                          message: '请选择价格单位',
                          trigger: 'change',
                        },
                      ]">
                        <a-select v-model:value="transaction.dealUnit" placeholder="请选择价格单位"
                          :disabled="transaction.dealStatus !== 1">
                          <a-select-option :value="0">元/月</a-select-option>
                          <a-select-option :value="1">元/年</a-select-option>
                          <a-select-option :value="2">元/平方米/年</a-select-option>
                          <a-select-option :value="3">元/平方米/月</a-select-option>
                          <a-select-option :value="4">一口价（元）</a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="20">
                    <a-col :span="8">
                      <a-form-item label="成交日期" :name="['transactions', index, 'dealDate']" :rules="[
                        {
                          required: transaction.dealStatus === 1,
                          message: '请选择成交日期',
                          trigger: 'change',
                        },
                      ]">
                        <a-date-picker v-model:value="transaction.dealDate" placeholder="选择成交日期" style="width: 100%"
                          :disabled="transaction.dealStatus !== 1" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="承租开始时间" :name="['transactions', index, 'dealBeginDate']" :rules="[
                        {
                          required: transaction.dealStatus === 1,
                          message: '请选择承租开始时间',
                          trigger: 'change',
                        },
                      ]">
                        <a-date-picker v-model:value="transaction.dealBeginDate" placeholder="选择承租开始时间"
                          style="width: 100%" :disabled="transaction.dealStatus !== 1" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="承租结束时间" :name="['transactions', index, 'dealEndDate']" :rules="[
                        {
                          required: transaction.dealStatus === 1,
                          message: '请选择承租结束时间',
                          trigger: 'change',
                        },
                      ]">
                        <a-date-picker v-model:value="transaction.dealEndDate" placeholder="选择承租结束时间"
                          style="width: 100%" :disabled="transaction.dealStatus !== 1" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="20">
                    <a-col :span="8">
                      <a-form-item label="是否内部承租再招租" :name="['transactions', index, 'dealInternal']">
                        <a-select v-model:value="transaction.dealInternal" placeholder="请选择">
                          <a-select-option :value="0">非内部承租再招租</a-select-option>
                          <a-select-option :value="1">内部承租再招租-承租</a-select-option>
                          <a-select-option :value="2">内部承租再招租-招租</a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="关联招租信息" :name="['transactions', index, 'dealCode']">
                        <a-select v-model:value="transaction.dealCode" mode="multiple" placeholder="请选择" show-search
                          :filter-option="false" @search="handleDealCodeSearch">
                          <a-select-option v-for="item in dealCodeOptions" :key="item.value" :value="item.value">
                            {{ item.label }}
                          </a-select-option>
                        </a-select>
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="20">
                    <a-col :span="8">
                      <a-form-item label="拟收总租金(万元)" :name="['transactions', index, 'planTotalRent']" :rules="[
                        {
                          required: transaction.dealStatus === 1,
                          message: '请输入拟收总租金',
                          trigger: 'blur',
                        },
                      ]">
                        <a-input-number v-model:value="transaction.planTotalRent" :precision="2" :controls="false"
                          style="width: 100%" :disabled="transaction.dealStatus !== 1" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="首年租金年收入(万元)" :name="['transactions', index, 'rentalIncome']" :rules="[
                        {
                          required: transaction.dealStatus === 1,
                          message: '请输入首年租金年收入',
                          trigger: 'blur',
                        },
                      ]">
                        <a-input-number v-model:value="transaction.rentalIncome" :precision="2" :controls="false"
                          style="width: 100%" :disabled="transaction.dealStatus !== 1" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-item label="租金年递增率(%)" :name="['transactions', index, 'rentEscalationRate']" :rules="[
                        {
                          required: transaction.dealStatus === 1,
                          message: '请输入租金年递增率',
                          trigger: 'blur',
                        },
                      ]">
                        <a-input v-model:value="transaction.rentEscalationRate" placeholder="请输入租金年递增率"
                          :disabled="transaction.dealStatus !== 1" />
                      </a-form-item>
                    </a-col>
                  </a-row>
                  <a-row :gutter="20">
                    <a-col :span="24">
                      <a-form-item label="合同文件" :label-col="{ span: 3 }" :wrapper-col="{ span: 16 }"
                        :name="['transactions', index, 'contractFile']">
                        <JUpload v-model:value="transaction.contractFile" :bizPath="'rent/contract'" :returnUrl="false"
                          :multiple="false" :maxCount="1" :removeConfirm="true" helpMessage="请上传合同主要内容附件" />
                      </a-form-item>
                    </a-col>
                    <a-col :span="24">
                      <a-form-item label="备注" :label-col="{ span: 3 }" :wrapper-col="{ span: 16 }"
                        :name="['transactions', index, 'remark']">
                        <a-input v-model:value="transaction.remark" placeholder="请输入备注" />
                      </a-form-item>
                    </a-col>
                  </a-row>

                  <!-- 租金明细部分 -->
                  <div class="rent-details-section" v-if="transaction.dealStatus === 1">
                    <div class="rent-details-header">
                      <h5>租金明细</h5>
                      <a-button type="primary" size="small" @click="addRentDetail(index)">
                        <Icon icon="ant-design:plus-outlined" />
                        新增租金明细
                      </a-button>
                    </div>
                    <div class="rent-details-table-wrapper">
                      <a-table :dataSource="transaction.rentDetails" :columns="rentDetailsColumns" :pagination="false"
                        size="small" :bordered="true" rowKey="uid" :scroll="{ x: 1200 }">
                        <template #bodyCell="{ column, record, index: detailIndex }">
                          <!-- 年份列 -->
                          <template v-if="column.dataIndex === 'year'">
                            <a-form-item :name="['transactions', index, 'rentDetails', detailIndex, 'year']"
                              :rules="{ required: true, message: '请选择年份' }" class="mb-0">
                              <a-input v-model:value="record.year" placeholder="请输入年份，如2024" size="small"
                                style="width: 100%" />
                            </a-form-item>
                          </template>

                          <!-- 季度列 -->
                          <template v-else-if="column.dataIndex === 'season'">
                            <a-form-item :name="['transactions', index, 'rentDetails', detailIndex, 'season']"
                              :rules="{ required: true, message: '请选择季度' }" class="mb-0">
                              <a-select v-model:value="record.season" size="small" style="width: 100%">
                                <a-select-option :value="1">一季度</a-select-option>
                                <a-select-option :value="2">二季度</a-select-option>
                                <a-select-option :value="3">三季度</a-select-option>
                                <a-select-option :value="4">四季度</a-select-option>
                              </a-select>
                            </a-form-item>
                          </template>

                          <!-- 应收租金列 -->
                          <template v-else-if="column.dataIndex === 'receivableRent'">
                            <a-form-item :name="['transactions', index, 'rentDetails', detailIndex, 'receivableRent']"
                              :rules="{ required: true, message: '请输入应收租金' }" class="mb-0">
                              <a-input-number v-model:value="record.receivableRent" :precision="2" :controls="false"
                                size="small" style="width: 100%"
                                @change="() => calculateUnpaidRent(record, index, detailIndex)" />
                            </a-form-item>
                          </template>

                          <!-- 实收租金列 -->
                          <template v-else-if="column.dataIndex === 'receiptsRent'">
                            <a-form-item :name="['transactions', index, 'rentDetails', detailIndex, 'receiptsRent']"
                              :rules="{ required: true, message: '请输入实收租金' }" class="mb-0">
                              <a-input-number v-model:value="record.receiptsRent" :precision="2" :controls="false"
                                size="small" style="width: 100%"
                                @change="() => calculateUnpaidRent(record, index, detailIndex)" />
                            </a-form-item>
                          </template>

                          <!-- 未收租金列 -->
                          <template v-else-if="column.dataIndex === 'unpaidRent'">
                            <a-form-item :name="['transactions', index, 'rentDetails', detailIndex, 'unpaidRent']"
                              class="mb-0">
                              <a-input-number v-model:value="record.unpaidRent" :precision="2" :controls="false"
                                size="small" style="width: 100%" disabled />
                            </a-form-item>
                          </template>

                          <!-- 违约租金列 -->
                          <template v-else-if="column.dataIndex === 'defaultRent'">
                            <a-form-item :name="['transactions', index, 'rentDetails', detailIndex, 'defaultRent']"
                              :rules="{ required: true, message: '请输入违约租金' }" class="mb-0">
                              <a-input-number v-model:value="record.defaultRent" :precision="2" :controls="false"
                                size="small" style="width: 100%" />
                            </a-form-item>
                          </template>

                          <!-- 减免租金列 -->
                          <template v-else-if="column.dataIndex === 'reductionRent'">
                            <a-form-item :name="['transactions', index, 'rentDetails', detailIndex, 'reductionRent']"
                              :rules="{ required: true, message: '请输入减免租金' }" class="mb-0">
                              <a-input-number v-model:value="record.reductionRent" :precision="2" :controls="false"
                                size="small" style="width: 100%" />
                            </a-form-item>
                          </template>

                          <!-- 未收租金原因列 -->
                          <template v-else-if="column.dataIndex === 'reason'">
                            <a-form-item :name="['transactions', index, 'rentDetails', detailIndex, 'reason']" :rules="[
                              {
                                required: record.unpaidRent > 0,
                                message: '请选择未收租金原因',
                                trigger: 'change',
                              },
                            ]" class="mb-0">
                              <a-select v-model:value="record.reason" size="small" style="width: 100%"
                                :disabled="!record.unpaidRent > 0">
                                <a-select-option :value="1">租户缴费流程未完成或其他突发性情况，延期支付</a-select-option>
                                <a-select-option :value="2">租户无法支付，催缴中</a-select-option>
                                <a-select-option :value="3">租户无法支付，诉讼或强制执行中</a-select-option>
                                <a-select-option :value="4">租户无法支付，通过诉讼或其他手段仍无法偿还，转为坏账</a-select-option>
                                <a-select-option :value="5">租户提前退租，后续用保证金、押金抵扣租金</a-select-option>
                                <a-select-option :value="6">未到缴交日期</a-select-option>
                                <a-select-option :value="7">其他</a-select-option>
                              </a-select>
                            </a-form-item>
                          </template>

                          <!-- 备注列 -->
                          <template v-else-if="column.dataIndex === 'detailsRemark'">
                            <a-form-item :name="['transactions', index, 'rentDetails', detailIndex, 'detailsRemark']"
                              :rules="[
                                {
                                  required: record.reason === 7,
                                  message: '请输入备注',
                                  trigger: 'blur',
                                },
                              ]" class="mb-0">
                              <a-input v-model:value="record.detailsRemark" size="small"
                                :disabled="record.reason !== 7" />
                            </a-form-item>
                          </template>

                          <!-- 操作列 -->
                          <template v-else-if="column.dataIndex === 'action'">
                            <a-button type="primary" danger size="small" @click="removeRentDetail(index, detailIndex)">
                              <Icon icon="ant-design:delete-outlined" />
                            </a-button>
                          </template>
                        </template>
                      </a-table>
                    </div>
                    <div v-if="transaction.rentDetails.length === 0" class="empty-hint">
                      暂无租金明细，请点击上方"新增租金明细"按钮添加。
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="formData.transactions.length === 0" class="empty-hint" style="padding: 20px;">
              暂无成交信息，请点击上方"新增成交信息"按钮添加。
            </div>
          </div>
        </div>

        <!-- 表单提交 -->
        <div class="form-footer">
          <a-button @click="resetForm">重置</a-button>
          <a-button type="primary" :loading="loading" @click="submitForm" style="margin-left: 12px">{{ submitButtonText
            }}</a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, nextTick } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { message, Modal } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import { saveOrUpdate, getDetail } from './rentInfo.api';
  import { JUpload } from '/@/components/Form/src/jeecg/components/JUpload';
  import { UploadTypeEnum } from '/@/components/Form/src/jeecg/components/JUpload/upload.data';
  import dayjs from 'dayjs';
  import { JAreaLinkage, JDictSelectTag, ApiSelect } from '/@/components/Form';

  const route = useRoute();
  const router = useRouter();

  // 表单引用
  const formRef = ref();

  // 页面状态
  const isEditMode = ref(false);
  const pageTitle = ref('新增租赁信息');
  const submitButtonText = ref('提交');
  const loading = ref(false);

  // 表格列定义
  const associatedAssetsColumns = [
    {
      title: '资产类型',
      dataIndex: 'assetType',
      key: 'assetType',
      width: 150,
      align: 'center',
    },
    {
      title: '资产名称（资产编号）',
      dataIndex: 'assetsCode',
      key: 'assetsCode',
      width: 200,
      align: 'center',
    },
    {
      title: '标的名称',
      dataIndex: 'targetName',
      key: 'targetName',
      width: 200,
      align: 'center',
    },
    {
      title: '标的所占面积（㎡）',
      dataIndex: 'area',
      key: 'area',
      width: 150,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 80,
      align: 'center',
      fixed: 'right',
    },
  ];

  const rentDetailsColumns = [
    {
      title: '年份',
      dataIndex: 'year',
      key: 'year',
      width: 100,
      align: 'center',
    },
    {
      title: '季度',
      dataIndex: 'season',
      key: 'season',
      width: 100,
      align: 'center',
    },
    {
      title: '应收租金(万元)',
      dataIndex: 'receivableRent',
      key: 'receivableRent',
      width: 120,
      align: 'center',
    },
    {
      title: '实收租金(万元)',
      dataIndex: 'receiptsRent',
      key: 'receiptsRent',
      width: 120,
      align: 'center',
    },
    {
      title: '未收租金(万元)',
      dataIndex: 'unpaidRent',
      key: 'unpaidRent',
      width: 120,
      align: 'center',
    },
    {
      title: '违约租金(万元)',
      dataIndex: 'defaultRent',
      key: 'defaultRent',
      width: 120,
      align: 'center',
    },
    {
      title: '减免租金(万元)',
      dataIndex: 'reductionRent',
      key: 'reductionRent',
      width: 120,
      align: 'center',
    },
    {
      title: '未收租金原因',
      dataIndex: 'reason',
      key: 'reason',
      width: 200,
      align: 'center',
    },
    {
      title: '备注',
      dataIndex: 'detailsRemark',
      key: 'detailsRemark',
      width: 200,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 80,
      align: 'center',
      fixed: 'right',
    },
  ];

  // 表单数据
  const formData = reactive({
    code: '',
    name: '',
    rentType: '',
    merchantsType: '',
    relatedPublicLeasing: '',
    manageUnit: '',
    reportOrNot: 1,
    operator: '张三',
    entryClerk: '张三',
    createTime: formatDateTime(new Date()),
    status: 0,
    remark: '',
    listingPrice: null as number | null,
    priceUnit: 0,
    lease: '',
    rentArea: null as number | null,
    listingOrg: '',
    listingLocation: [] as string[],
    lessorName: '',
    lessorType: 0,
    address: '',
    registration: '',
    registeredCapital: null as number | null,
    economicType: 0,
    legalRepresentative: '',
    industry: '',
    listingStartDate: '',
    listingEndDate: '',
    contentDescription: '',
    fileList: [] as any[],
    associatedAssets: [] as Array<{
      assetType: number;
      assetsCode: string;
      targetName: string;
      area: number | null;
      uid?: string;
    }>,
    transactions: [] as Array<{
      targetName: string;
      dealStatus: number;
      targetArea: number | null;
      dealName: string;
      dealPrice: number | null;
      dealUnit: number;
      dealDate: string;
      dealBeginDate: string;
      dealEndDate: string;
      dealInternal: number;
      dealCode: string[];
      planTotalRent: number | null;
      rentalIncome: number | null;
      rentEscalationRate: string;
      contractFile: any[];
      remark: string;
      rentDetails: Array<{
        year: string;
        season: number;
        receivableRent: number | null;
        receiptsRent: number | null;
        unpaidRent: number | null;
        defaultRent: number | null;
        reductionRent: number | null;
        reason: number;
        detailsRemark: string;
        uid?: string;
      }>;
      isTargetAreaDisabled: boolean;
    }>,
  });

  // 初始数据备份
  const initialFormData = ref(null);

  // 选项数据
  const enterpriseOptions = ref([
    { value: 0, label: '厦门市城市建设发展投资有限公司' },
    { value: 1, label: '厦门市地热资源管理有限公司' },
    { value: 2, label: '厦门兴地房屋征迁服务有限公司' },
    { value: 3, label: '厦门地丰置业有限公司' },
    { value: 4, label: '图智策划咨询（厦门）有限公司' },
    { value: 5, label: '厦门市集众祥和物业管理有限公司' },
    { value: 6, label: '厦门市人居乐业物业服务有限公司' },
  ]);

  const lessorOptions = ref<Array<{ value: string; label: string }>>([]);
  const relatedPublicLeasingOptions = ref<Array<{ value: string; label: string }>>([]);
  const assetOptions = ref<Array<{ value: string; label: string }>>([]);
  const dealCodeOptions = ref<Array<{ value: string; label: string }>>([]);

  // 出租方模拟数据
  const lessorMockData = [
    {
      name: '厦门市城市建设发展投资有限公司',
      type: 0,
      address: '福建省厦门市思明区',
      registration: '厦门市思明区会展路123号',
      registeredCapital: 50000,
      economicType: 1,
      legalRepresentative: '李建国',
      industry: '建筑业'
    },
    {
      name: '厦门市地热资源管理有限公司',
      type: 0,
      address: '福建省厦门市湖里区',
      registration: '厦门市湖里区金山街道456号',
      registeredCapital: 10000,
      economicType: 2,
      legalRepresentative: '王海洋',
      industry: '能源业'
    },
    {
      name: '图智策划咨询（厦门）有限公司',
      type: 0,
      address: '广东省深圳市南山区',
      registration: '深圳市南山区科技园路789号',
      registeredCapital: 500,
      economicType: 4,
      legalRepresentative: '陈思远',
      industry: '专业服务业'
    },
    {
      name: '厦门市集众祥和物业管理有限公司',
      type: 1,
      address: '浙江省杭州市西湖区',
      registration: '杭州市西湖区文一路101号',
      registeredCapital: 100,
      economicType: 6,
      legalRepresentative: '张伟',
      industry: '房地产业'
    }
  ];

  // 资产模拟数据
  const assetMockData = [
    { name: '总部办公大楼', code: 'ZC001', area: 5000.00 },
    { name: '城东物流仓库', code: 'ZC002', area: 12000.50 },
    { name: '海滨大道广告牌', code: 'ZC003', area: 100.00 },
    { name: '开发区研发中心', code: 'ZC004', area: 8500.75 },
    { name: '中山路临街商铺', code: 'ZC005', area: 300.0 },
    { name: '城南停车场地块', code: 'ZC006', area: 25000.00 },
    { name: '西郊生产厂房', code: 'ZC007', area: 15000.00 },
    { name: 'CBD金融中心写字楼', code: 'ZC008', area: 7500.20 },
    { name: '度假村别墅群', code: 'ZC009', area: 30000.00 },
    { name: '历史文化街区建筑', code: 'ZC010', area: 2500.00 }
  ];

  // 地区选项
  const locationOptions = ref([
    {
      value: 'fujian',
      label: '福建省',
      children: [
        { value: 'xiamen', label: '厦门市' },
        { value: 'fuzhou', label: '福州市' },
        { value: 'quanzhou', label: '泉州市' },
      ]
    },
    {
      value: 'guangdong',
      label: '广东省',
      children: [
        { value: 'guangzhou', label: '广州市' },
        { value: 'shenzhen', label: '深圳市' },
        { value: 'zhuhai', label: '珠海市' },
      ]
    },
    {
      value: 'zhejiang',
      label: '浙江省',
      children: [
        { value: 'hangzhou', label: '杭州市' },
        { value: 'ningbo', label: '宁波市' },
        { value: 'wenzhou', label: '温州市' },
      ]
    }
  ]);

  // 状态选项
  const statusOptions = computed(() => [
    { value: 0, label: '草稿', disabled: false },
    { value: 1, label: '备案', disabled: false },
    { value: 2, label: '撤回', disabled: true },
    { value: 4, label: '作废', disabled: true }
  ]);

  // 表单验证规则
  const rules = reactive({
    name: [{ required: true, message: '请输入租赁资产包名称', trigger: 'blur' }],
    rentType: [{ required: true, message: '请选择出租方式', trigger: 'change' }],
    merchantsType: [{ required: true, message: '请选择是否专业化招商', trigger: 'change' }],
    manageUnit: [{ required: true, message: '请选择管理单位', trigger: 'change' }],
    reportOrNot: [{ required: true, message: '请选择是否报送国资委', trigger: 'change' }],
    operator: [{ required: true, message: '请输入经办人', trigger: 'blur' }],
    status: [{ required: true, message: '请选择状态', trigger: 'change' }],
    listingPrice: [{ required: true, message: '请输入挂牌价格', trigger: 'blur' }],
    priceUnit: [{ required: true, message: '请选择价格单位', trigger: 'change' }],
    lease: [{ required: true, message: '请输入租期', trigger: 'blur' }],
    lessorName: [{ required: true, message: '请输入出租方名称', trigger: 'blur' }],
    contentDescription: [{ required: true, message: '请输入内容描述', trigger: 'blur' }],
    relatedPublicLeasing: [
      {
        validator: (rule, value, callback) => {
          if (formData.rentType === '4' && !value) {
            callback(new Error('请选择关联公开招租'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ],
    listingOrg: [
      {
        validator: (rule, value, callback) => {
          if (formData.rentType === '1' && !value) {
            callback(new Error('请输入挂牌机构'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    listingLocation: [
      {
        validator: (rule, value, callback) => {
          if (formData.rentType === '1' && (!value || value.length === 0)) {
            callback(new Error('请选择挂牌机构所在地区'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ],
    listingStartDate: [
      {
        validator: (rule, value, callback) => {
          if (['0', '1', '2'].includes(formData.rentType) && !value) {
            callback(new Error('请选择挂牌（公示）开始时间'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ],
    listingEndDate: [
      {
        validator: (rule, value, callback) => {
          if (['0', '1', '2'].includes(formData.rentType) && !value) {
            callback(new Error('请选择挂牌（公示）截止时间'));
          } else {
            callback();
          }
        },
        trigger: 'change'
      }
    ],
  });

  // 工具函数
  function formatDateTime(date: Date) {
    const d = new Date(date);
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}:${String(d.getSeconds()).padStart(2, '0')}`;
  }

  // 远程搜索方法
  function handleRelatedPublicLeasingSearch(query: string) {
    const mockData = [
      { value: '*********', label: '核心商圈A座写字楼资产包 (*********)' },
      { value: '*********', label: '高新技术园区厂房资产包 (*********)' },
      { value: '*********', label: '物流仓储中心资产包 (*********)' },
      { value: '*********', label: '城市广场一期商铺资产包 (*********)' },
      { value: '*********', label: '滨海度假酒店资产包 (*********)' },
      { value: '*********', label: '老城区改造项目资产包 (*********)' },
      { value: '*********', label: '数据中心机房资产包 (*********)' },
      { value: '*********', label: '文化创意园办公空间资产包 (*********)' },
      { value: '*********', label: '连锁超市门店资产包 (*********)' },
      { value: '*********', label: '社区沿街商铺资产包 (*********)' },
    ];

    if (query) {
      relatedPublicLeasingOptions.value = mockData.filter(item => {
        return item.label.toLowerCase().includes(query.toLowerCase());
      });
    } else {
      relatedPublicLeasingOptions.value = mockData;
    }
  }

  function handleAssetSearch(query: string) {
    const options = assetMockData.map(item => ({
      value: item.code,
      label: `${item.name} (${item.code})`
    }));

    if (query) {
      assetOptions.value = options.filter(item => {
        return item.label.toLowerCase().includes(query.toLowerCase());
      });
    } else {
      assetOptions.value = options;
    }
  }

  function handleDealCodeSearch(query: string) {
    if (!query) return;
    dealCodeOptions.value = Array.from({ length: 10 }).map((_, i) => ({
      value: `GG202300${i}`,
      label: `招租公告${query}${i}（GG202300${i}）`
    }));
  }

  function handleLessorSearch(query: string) {
    const options = lessorMockData.map(item => ({
      value: item.name,
      label: item.name
    }));

    if (query) {
      lessorOptions.value = options.filter(item => {
        return item.label.toLowerCase().includes(query.toLowerCase());
      });
    } else {
      lessorOptions.value = options;
    }
  }

  // 事件处理方法
  function handleRentTypeChange() {
    // 重置相关字段
    if (formData.rentType !== '4') {
      formData.relatedPublicLeasing = '';
    }
    if (formData.rentType !== '1') {
      formData.listingOrg = '';
      formData.listingLocation = [];
    }
    if (!['0', '1', '2'].includes(formData.rentType)) {
      formData.listingStartDate = '';
      formData.listingEndDate = '';
    }
  }

  function handleLessorChange(value: string) {
    const selectedLessor = lessorMockData.find(item => item.name === value);
    if (selectedLessor) {
      formData.lessorType = selectedLessor.type;
      formData.address = selectedLessor.address;
      formData.registration = selectedLessor.registration;
      formData.registeredCapital = selectedLessor.registeredCapital;
      formData.economicType = selectedLessor.economicType;
      formData.legalRepresentative = selectedLessor.legalRepresentative;
      formData.industry = selectedLessor.industry;
    }
  }

// 关联资产相关方法
function addAssociatedAsset() {
  formData.associatedAssets.push({
    assetType: 1,
    assetsCode: '',
    targetName: '',
    area: null,
    uid: generateUid()
  });
}

function removeAssociatedAsset(index: number) {
  formData.associatedAssets.splice(index, 1);
}

function handleAssetChange(value: string, index: number) {
  const selectedAsset = assetMockData.find(item => item.code === value);
  if (selectedAsset) {
    formData.associatedAssets[index].targetName = selectedAsset.name;
    formData.associatedAssets[index].area = selectedAsset.area;
  }
}

function handleAssetTypeChange(changedAsset: any) {
  formData.transactions.forEach(transaction => {
    if (transaction.targetName === changedAsset.targetName) {
      if (![0, 1].includes(changedAsset.assetType)) {
        transaction.targetArea = 0;
        transaction.isTargetAreaDisabled = true;
      } else {
        transaction.targetArea = changedAsset.area;
        transaction.isTargetAreaDisabled = false;
      }
      const transactionIndex = formData.transactions.indexOf(transaction);
      formRef.value?.validateField(['transactions', transactionIndex, 'targetArea']);
    }
  });
}

// 成交信息相关方法
function addTransaction() {
  formData.transactions.push({
    targetName: '',
    dealStatus: 0,
    targetArea: null,
    dealName: '',
    dealPrice: null,
    dealUnit: 0,
    dealDate: '',
    dealBeginDate: '',
    dealEndDate: '',
    dealInternal: 0,
    dealCode: [],
    planTotalRent: null,
    rentalIncome: null,
    rentEscalationRate: '',
    contractFile: [],
    remark: '',
    rentDetails: [],
    isTargetAreaDisabled: false
  });
}

function removeTransaction(index: number) {
  formData.transactions.splice(index, 1);
}

function handleTransactionTargetChange(transaction: any) {
  const linkedAsset = formData.associatedAssets.find(asset => asset.targetName === transaction.targetName);
  if (linkedAsset) {
    if (![0, 1].includes(linkedAsset.assetType)) {
      transaction.targetArea = 0;
      transaction.isTargetAreaDisabled = true;
    } else {
      transaction.targetArea = linkedAsset.area;
      transaction.isTargetAreaDisabled = false;
    }
  } else {
    transaction.targetArea = null;
    transaction.isTargetAreaDisabled = false;
  }
  formRef.value?.validateField(['transactions', formData.transactions.indexOf(transaction), 'targetArea']);
}

function handleDealStatusChange(index: number) {
  nextTick(() => {
    formRef.value?.validate();
  });
}

function validateTargetArea(rule: any, value: any, callback: any, transaction: any) {
  if (transaction.dealStatus === 1 && !transaction.isTargetAreaDisabled && (value === null || value === undefined)) {
    callback(new Error('请输入标的面积'));
  } else {
    callback();
  }
}

// 租金明细相关方法
function addRentDetail(transactionIndex: number) {
  if (transactionIndex > -1) {
    formData.transactions[transactionIndex].rentDetails.push({
      year: '', // 使用字符串而不是日期对象
      season: 1,
      receivableRent: null,
      receiptsRent: null,
      unpaidRent: null,
      defaultRent: null,
      reductionRent: null,
      reason: 6,
      detailsRemark: '',
      uid: generateUid()
    });
  }
}

function removeRentDetail(transactionIndex: number, detailIndex: number) {
  if (transactionIndex > -1) {
    formData.transactions[transactionIndex].rentDetails.splice(detailIndex, 1);
  }
}

function calculateUnpaidRent(detail: any, transactionIndex: number, detailIndex: number) {
  const receivable = detail.receivableRent || 0;
  const receipts = detail.receiptsRent || 0;
  detail.unpaidRent = Number((receivable - receipts).toFixed(2));

  nextTick(() => {
    formRef.value?.validateField(['transactions', transactionIndex, 'rentDetails', detailIndex, 'reason']);
  });
}

// 文件上传处理方法 - 不再需要，由JUpload组件处理
// function handleUpload(options: any) {
//   const { file, onSuccess } = options;
//   
//   // 模拟文件上传
//   setTimeout(() => {
//     const fileInfo = {
//       uid: file.uid,
//       name: file.name,
//       status: 'done',
//       url: URL.createObjectURL(file),
//       size: file.size,
//       type: file.type
//     };
//     
//     if (onSuccess) {
//       onSuccess(fileInfo);
//     }
//   }, 1000);
// }

// 表单操作方法
function resetForm() {
  if (initialFormData.value) {
    Object.assign(formData, JSON.parse(JSON.stringify(initialFormData.value)));
  }
  nextTick(() => {
    formRef.value?.clearValidate();
  });
}

async function submitForm() {
  try {
    await formRef.value?.validate();

    loading.value = true;
    const submitData = JSON.parse(JSON.stringify(formData));

    // 处理数据格式
    if (submitData.listingLocation && Array.isArray(submitData.listingLocation)) {
      submitData.listingLocation = submitData.listingLocation.join(',');
    }

    // 处理日期字段
    if (submitData.listingStartDate) {
      submitData.listingStartDate = submitData.listingStartDate.format('YYYY-MM-DD');
    }

    if (submitData.listingEndDate) {
      submitData.listingEndDate = submitData.listingEndDate.format('YYYY-MM-DD');
    }

    // 处理交易信息中的日期
    if (submitData.transactions && Array.isArray(submitData.transactions)) {
      submitData.transactions.forEach(transaction => {
        // 处理交易日期
        if (transaction.dealDate) {
          transaction.dealDate = transaction.dealDate.format('YYYY-MM-DD');
        }

        if (transaction.dealBeginDate) {
          transaction.dealBeginDate = transaction.dealBeginDate.format('YYYY-MM-DD');
        }

        if (transaction.dealEndDate) {
          transaction.dealEndDate = transaction.dealEndDate.format('YYYY-MM-DD');
        }

        // 处理租金明细
        if (transaction.rentDetails && Array.isArray(transaction.rentDetails)) {
          transaction.rentDetails.forEach(detail => {
            // 确保年份是字符串格式
            if (detail.year) {
              // 确保年份是字符串
              detail.year = String(detail.year);
              // 如果是日期对象，转换为字符串
              if (detail.year instanceof Date) {
                detail.year = detail.year.getFullYear().toString();
              }
              // 如果包含短横线，可能是日期格式，只取年份部分
              if (detail.year.includes('-')) {
                detail.year = detail.year.split('-')[0];
              }
            } else {
              detail.year = '';
            }
          });
        }
      });
    }

    const result = await saveOrUpdate(submitData, isEditMode.value);

    if (result) {
      message.success(isEditMode.value ? '更新成功!' : '提交成功!');
      router.push('/rent/info');
    }
  } catch (error) {
    console.error('表单提交失败:', error);
    message.error('表单校验失败!');
  } finally {
    loading.value = false;
  }
}

// 初始化
onMounted(async () => {
  // 同时支持路径参数和查询参数
  const id = route.params.id || route.query.id;
  console.log('路由参数:', route.params, route.query, '获取到ID:', id);

  if (id) {
    isEditMode.value = true;
    pageTitle.value = '编辑租赁信息';
    submitButtonText.value = '更新';

    try {
      loading.value = true;
      const data = await getDetail(id);
      if (data) {
        // 处理地区数据格式
        if (data.listingLocation && typeof data.listingLocation === 'string') {
          data.listingLocation = data.listingLocation.split(',');
        }

        // 处理关联资产数据，添加唯一ID
        if (data.associatedAssets && Array.isArray(data.associatedAssets)) {
          data.associatedAssets = data.associatedAssets.map(asset => ({
            ...asset,
            uid: generateUid()
          }));
        } else {
          data.associatedAssets = [];
        }

        // 处理日期字段
        if (data.listingStartDate) {
          data.listingStartDate = data.listingStartDate ? dayjs(data.listingStartDate) : null;
        }

        if (data.listingEndDate) {
          data.listingEndDate = data.listingEndDate ? dayjs(data.listingEndDate) : null;
        }

        // 处理交易信息数据
        if (data.transactions && Array.isArray(data.transactions)) {
          data.transactions = data.transactions.map(transaction => {
            // 处理租金明细数据，添加唯一ID
            if (transaction.rentDetails && Array.isArray(transaction.rentDetails)) {
              transaction.rentDetails = transaction.rentDetails.map(detail => {
                // 处理年份字段，确保是字符串格式
                let yearValue = detail.year;
                if (yearValue) {
                  // 确保年份是字符串
                  yearValue = String(yearValue);
                  // 如果是日期对象，转换为字符串
                  if (yearValue instanceof Date) {
                    yearValue = yearValue.getFullYear().toString();
                  }
                  // 如果包含短横线，可能是日期格式，只取年份部分
                  if (yearValue.includes('-')) {
                    yearValue = yearValue.split('-')[0];
                  }
                } else {
                  yearValue = '';
                }

                return {
                  ...detail,
                  year: yearValue,
                  uid: generateUid()
                };
              });
            } else {
              transaction.rentDetails = [];
            }

            // 处理日期字段，确保是有效的日期格式
            const processDate = (dateStr) => {
              if (!dateStr) return null;
              return dayjs(dateStr);
            };

            // 查找关联资产，确定是否禁用标的面积
            const linkedAsset = data.associatedAssets?.find(asset =>
              asset.targetName === transaction.targetName
            );

            // 确保交易信息中的其他字段正确初始化
            return {
              ...transaction,
              dealDate: processDate(transaction.dealDate),
              dealBeginDate: processDate(transaction.dealBeginDate),
              dealEndDate: processDate(transaction.dealEndDate),
              isTargetAreaDisabled: linkedAsset ? ![0, 1].includes(linkedAsset.assetType) : false,
              contractFile: transaction.contractFile || []
            };
          });
        } else {
          data.transactions = [];
        }

        // 确保文件列表正确初始化
        data.fileList = data.fileList || [];

        // 将处理后的数据赋值给表单数据
        Object.assign(formData, data);

        console.log('加载的编辑数据:', JSON.stringify(data));
      }
    } catch (error) {
      console.error('获取详情失败:', error);
      message.error('获取详情失败');
    } finally {
      loading.value = false;
    }
  }

  // 备份初始数据
  initialFormData.value = JSON.parse(JSON.stringify(formData));

  // 初始化选项数据
  handleRelatedPublicLeasingSearch('');
  handleAssetSearch('');
  handleLessorSearch('');
});

// 生成唯一ID
function generateUid() {
  return 'uid_' + new Date().getTime() + '_' + Math.floor(Math.random() * 1000);
}
</script>

<style lang="less" scoped>
.rent-form {
  .simple-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
    padding: 16px 24px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  .form-card {
    background: white;
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .form-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      border-bottom: 1px solid #f0f0f0;

      .form-card-title {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 500;
        color: #333;

        .title-icon {
          margin-right: 8px;
          color: #1890ff;
        }
      }

      .form-card-action {
        .ant-btn {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }

    .form-card-body {
      padding: 24px;
    }
  }

  .form-footer {
    text-align: center;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .help-text {
    font-size: 12px;
    color: #909399;
    line-height: 1.5;
    margin-top: 5px;
  }

  .empty-data {
    color: #909399;
    text-align: center;
    padding: 20px 0;
  }

  .empty-hint {
    padding: 20px;
    text-align: center;
    color: #909399;
    font-size: 14px;
    background-color: #fafafa;
    border-radius: 4px;
    margin-top: 10px;
  }

  .transaction-item {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    margin-bottom: 16px;
    overflow: hidden;

    .transaction-item-header {
      background-color: #fafafa;
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #e8e8e8;

      h4 {
        margin: 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .transaction-item-body {
      padding: 20px;
    }
  }

  .rent-details-section {
    margin-top: 20px;
    border-top: 1px solid #e8e8e8;
    padding-top: 20px;

    .rent-details-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h5 {
        margin: 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .rent-details-table-wrapper {
      margin-bottom: 16px;
    }
  }

  .required::before {
    content: '*';
    color: #ff4d4f;
    margin-right: 4px;
  }

  // 表格样式
  :deep(.ant-table-small) {
    font-size: 12px;

    .ant-table-thead>tr>th {
      background-color: #fafafa;
      font-weight: 600;
      padding: 8px;
    }

    .ant-table-tbody>tr>td {
      padding: 4px 8px;
    }
  }

  // 表格中的表单项样式
  :deep(.ant-table-cell) {
    .ant-form-item {
      margin-bottom: 0;
    }
  }

  // 表格内表单项标签隐藏
  :deep(.ant-table .ant-form-item-label) {
    display: none;
  }

  // 表格内表单控件宽度
  :deep(.ant-table .ant-form-item-control) {
    width: 100%;
  }

  .mb-0 {
    margin-bottom: 0 !important;
  }

  // 统一设置表单项标签宽度
  :deep(.ant-form-item-label) {
    width: 180px;
    min-width: 180px;
    text-align: right;
    padding-right: 8px;
  }

  :deep(.ant-form-item-label > label) {
    width: 100%;
    justify-content: flex-end;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .form-card-body {
      padding: 15px;
    }

    .transaction-item-body {
      padding: 15px;
    }
  }
}
</style>
