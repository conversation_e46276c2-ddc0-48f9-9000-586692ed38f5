import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { Tag } from 'ant-design-vue';
import { h } from 'vue';
import { render } from '/@/utils/common/renderUtils';
import { getCompanyHandle, getUserCompany } from '/@/api/common/api';

// 表格列配置
export const columns: BasicColumn[] = [
  {
    title: '信息发布编码',
    dataIndex: 'code',
    width: 150,
    ellipsis: true,
  },
  {
    title: '公告标题',
    dataIndex: 'name',
    width: 200,
    ellipsis: true,
  },
  {
    title: '所属企业',
    dataIndex: 'companyNameString',
    width: 200,
  },
  {
    title: '管理单位',
    dataIndex: 'manageUnitName',
    width: 200,
    defaultHidden: true,
    ellipsis: true,
  },
  {
    title: '是否报送国资委',
    dataIndex: 'reportOrNot',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '经办人',
    dataIndex: 'operator',
    width: 100,
    ellipsis: true,
  },
  {
    title: '招租方式',
    dataIndex: 'rentType',
    width: 150,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'rent_type');
    },
  },
  {
    title: '公示日期',
    dataIndex: 'publicTime',
    width: 120,
  },
  {
    title: '附件',
    dataIndex: 'files',
    width: 120,
    customRender: ({ text }) => {
      if (text && Array.isArray(text) && text.length > 0) {
        return h(Tag, { color: 'blue', class: 'file-count' }, `${text.length}个附件`);
      }
      return '无附件';
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'record_status');
    },
  },
  {
    title: '发布平台',
    dataIndex: 'publishPlatform',
    width: 150,
    defaultHidden: true,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'publish_platform');
    },
  },
  {
    title: '发布状态',
    dataIndex: 'publishStatus',
    width: 120,
    defaultHidden: true,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'notice_publish_status');
    },
  },
  {
    title: '发布失败原因',
    dataIndex: 'publishFailReason',
    width: 180,
    defaultHidden: true,
    ellipsis: true,
  },
  {
    title: '审核状态',
    dataIndex: 'auditStatus',
    width: 120,
    defaultHidden: true,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'audit_status');
    },
  },
  {
    title: '审核不通过原因',
    dataIndex: 'auditRejectReason',
    width: 180,
    defaultHidden: true,
    ellipsis: true,
  },
  {
    title: '推送结果',
    dataIndex: 'pushResult',
    width: 120,
    defaultHidden: true,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'push_result');
    },
  },
  {
    title: '录入人',
    dataIndex: 'entryClerk',
    width: 100,
    ellipsis: true,
  },
  {
    title: '录入时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 150,
    defaultHidden: true,
  },
];

// 搜索表单配置
export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '公告标题',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入公告标题',
    },
  },
  {
    field: 'companyName',
    label: '所属企业',
    component: 'ApiSelect',
    componentProps: {
      api: getUserCompany,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
      placeholder: '请选择所属企业',
      style: { width: '100%' },
    },
    colProps: { span: 6 },
  },
  {
    field: 'status',
    label: '状态',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'record_status',
      placeholder: '请选择状态',
    },
    colProps: { span: 6 },
  },
  {
    field: 'code',
    label: '信息发布编码',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入信息发布编码',
    },
  },
  {
    field: 'rentType',
    label: '招租方式',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'rent_type',
      placeholder: '请选择招租方式',
    },
    colProps: { span: 6 },
  },
  {
    field: 'manageUnit',
    label: '管理单位',
    component: 'ApiSelect',
    componentProps: {
      api: getCompanyHandle,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
      placeholder: '请选择管理单位',
    },
    colProps: { span: 6 },
  },
  {
    field: 'reportOrNot',
    label: '是否报送国资委',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'yes_no',
      placeholder: '请选择',
    },
    colProps: { span: 6 },
  },
  {
    field: 'operator',
    label: '经办人',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    field: 'publicTime',
    label: '公示时间范围',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'publishPlatform',
    label: '发布平台',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'publish_platform',
      placeholder: '请选择发布平台',
    },
    colProps: { span: 6 },
  },
  {
    field: 'publishState',
    label: '发布状态',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'notice_publish_status',
      placeholder: '请选择发布状态',
    },
    colProps: { span: 6 },
  },
  {
    field: 'auditState',
    label: '审核状态',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'audit_status',
      placeholder: '请选择审核状态',
    },
    colProps: { span: 6 },
  },
  {
    field: 'pushResult',
    label: '推送结果',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'push_result',
      placeholder: '请选择推送结果',
    },
    colProps: { span: 6 },
  },
  {
    field: 'entryClerk',
    label: '录入人',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入录入人',
    },
  },
  {
    field: 'createTime',
    label: '录入时间',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'updateTime',
    label: '更新时间',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'auditFailReason',
    label: '审核不通过原因',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入审核不通过原因关键词',
    },
  },
];
