<template>
  <div class="notice-list">
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleCreate">新增</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>

    <!-- 关联资产包弹窗 -->
    <LinkAssetsModal @register="registerLinkAssetsModal" @success="handleLinkAssetsSuccess" />
  </div>
</template>

<script lang="ts" name="NoticeList" setup>
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useRouter } from 'vue-router';
  import { useModal } from '/@/components/Modal';
  import { columns, searchFormSchema } from './notice.data';
  import { getNoticeList, deleteNotice } from './notice.api';
  import LinkAssetsModal from './components/LinkAssetsModal.vue';

  const { createMessage } = useMessage();
  const router = useRouter();

  // 关联资产包弹窗注册
  const [registerLinkAssetsModal, { openModal: openLinkAssetsModal }] = useModal();

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'notice-list',
    tableProps: {
      title: '招租公告信息列表',
      api: getNoticeList,
      columns: columns,
      size: 'small',
      canResize: false,
      // 显示序号列
      showIndexColumn: true,
      // 显示表格设置
      showTableSetting: true,
      // 表格设置配置
      tableSetting: {
        // 缓存键名
        cacheKey: 'notice_list',
        // 显示列设置
        setting: true,
        // 显示刷新按钮
        redo: false,
        // 显示尺寸调整
        size: false,
        // 显示全屏按钮
        fullScreen: false,
      },
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 120,
        // 显示展开/收起按钮
        showAdvancedButton: true,
        // 超过4列时默认折叠
        autoAdvancedCol: 4,
        baseColProps: {
          xs: 24,
          sm: 12,
          md: 6,
          lg: 6,
          xl: 6,
          xxl: 6,
        },
        fieldMapToTime: [
          ['publicTime', ['publicTimeMin', 'publicTimeMax'], 'YYYY-MM-DD'],
          ['createTime', ['createTimeMin', 'createTimeMax'], 'YYYY-MM-DD'],
          ['updateTime', ['updateTimeMin', 'updateTimeMax'], 'YYYY-MM-DD'],
        ],
      },
      actionColumn: {
        width: 220,
        fixed: 'right',
      },
      pagination: {
        current: 1,
        pageSize: 10,
        pageSizeOptions: ['10', '20', '50', '100'],
      },
    },
  });

  // 注册table数据
  const [registerTable, { reload }, { rowSelection }] = tableContext;

  /**
   * 新增事件
   */
  function handleCreate() {
    router.push('/rent/notice/add');
  }

  /**
   * 编辑事件
   */
  async function handleEdit(record: Recordable) {
    router.push(`/rent/notice/edit/${record.id}`);
  }

  /**
   * 删除事件
   */
  async function handleDelete(record: Recordable) {
    await deleteNotice(record.id);
    createMessage.success('删除成功');
    reload();
  }

  /**
   * 关联资产包
   */
  function handleLinkAssets(record: Recordable) {
    openLinkAssetsModal(true, {
      record,
    });
  }

  /**
   * 关联资产包成功
   */
  function handleLinkAssetsSuccess() {
    reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '关联资产包',
        onClick: handleLinkAssets.bind(null, record),
      },
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped>
  @import './index.less';
</style> 