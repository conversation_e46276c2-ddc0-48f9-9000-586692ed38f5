import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/biz/qrCode/queryPage',
  exportXls = '/biz/qrCode/excel/export',
  exportAll = '/mock/qrcode/exportAll',
}

export const exportExcel = Api.exportXls;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.post({ url: Api.list, params });

/**
 * 导出
 * @param params
 */
export const exportQRCode = (params) => defHttp.get({ url: Api.exportXls, params, responseType: 'blob' });

/**
 * 全部导出
 * @param params
 */
export const exportAllQRCode = (params) => defHttp.get({ url: Api.exportAll, params, responseType: 'blob' }); 