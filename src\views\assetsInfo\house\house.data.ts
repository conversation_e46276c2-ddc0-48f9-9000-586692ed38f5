import { BasicColumn } from '/@/components/Table';
import { h } from 'vue';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { getCompanyHandle, getUserCompany } from '/@/api/common/api';

export const columns: BasicColumn[] = [
  {
    title: '资产编号',
    dataIndex: 'code',
    width: 150,
    fixed: 'left',
  },
  {
    title: '企业自定义编号',
    dataIndex: 'enterpriseCode',
    width: 150,
  },
  {
    title: '资产项目（资产名称）',
    dataIndex: 'name',
    width: 160,
    ellipsis: true,
  },
  {
    title: '所属集团',
    dataIndex: 'groupName',
    width: 160,
    customRender: ({ text }) => {
      return render.renderDict(text, 'group_name');
    },
  },
  {
    title: '所属企业',
    dataIndex: 'companyNameString',
    width: 160,
  },
  {
    title: '管理单位',
    dataIndex: 'manageUnitName',
    width: 160,
  },
  {
    title: '权属单位',
    dataIndex: 'ownUnit',
    width: 150,
  },
  {
    title: '详细地址',
    dataIndex: 'address',
    width: 200,
  },
  {
    title: '是否报送国资委',
    dataIndex: 'reportOrNot',
    width: 150,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => {
      const statusMap = {
        0: { text: '草稿', color: 'default' },
        1: { text: '备案', color: 'success' },
        2: { text: '撤回', color: 'warning' },
        4: { text: '作废', color: 'error' },
      };
      const status = statusMap[text];
      return status ? render.renderTag(status.text, status.color) : '';
    },
  },
  {
    title: '房屋用途（运营方式和情况）',
    dataIndex: 'houseTypes',
    width: 180,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'house_types');
    },
  },
  {
    title: '具体的房屋用途',
    dataIndex: 'useTypeInput',
    width: 150,
  },
  {
    title: '资产取得日期',
    dataIndex: 'gainDate',
    width: 120,
  },
  {
    title: '资产入账日期',
    dataIndex: 'assetEntryDate',
    width: 120,
  },
  {
    title: '房屋性质',
    dataIndex: 'source',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'house_property');
    },
  },
  {
    title: '资产总面积(㎡)',
    dataIndex: 'totalArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '可租面积(㎡)',
    dataIndex: 'rentableArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '产权面积(㎡)',
    dataIndex: 'propertyArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '非产权面积(㎡)',
    dataIndex: 'notPropertyArea',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '资产原值(元)',
    dataIndex: 'assetsAmount',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '账面价值(元)',
    dataIndex: 'bookAmount',
    width: 150,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '账面价值时点',
    dataIndex: 'dateOfBookValue',
    width: 180,
  },
  {
    title: '是否有产权证',
    dataIndex: 'property',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'land_property_type');
    },
  },
  {
    title: '产权证获得日期',
    dataIndex: 'warrantDate',
    width: 150,
  },
  {
    title: '代管委托方',
    dataIndex: 'custodyEntrustingParty',
    width: 150,
  },
  {
    title: '是否账外',
    dataIndex: 'offAccount',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '是否投保',
    dataIndex: 'insuranceOrNot',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '是否抵押或质押',
    dataIndex: 'mortgageOrNot',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '是否危房',
    dataIndex: 'dangerousHouseOrNot',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '是否竣工财务结算办理',
    dataIndex: 'completionOrNot',
    width: 180,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '是否拖欠工程款',
    dataIndex: 'owingOrNot',
    width: 150,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '是否具有盘活价值',
    dataIndex: 'vitalizeOrNot',
    width: 150,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '工作进展',
    dataIndex: 'workProgress',
    width: 200,
  },
  {
    title: '存在问题',
    dataIndex: 'problems',
    width: 200,
  },
  {
    title: '资产使用状态',
    dataIndex: 'assetsStatus',
    width: 200,
    customRender: ({ text }) => {
      const textArray = text ? text.split(',') : [];
      return h(
        'div',
        textArray.map((item) => {
          const finalText = render.renderDict(item, 'land_assets_status', true);
          return h('span', finalText);
        })
      );
    },
  },
  {
    title: '空置闲置面积(㎡)',
    dataIndex: 'idleArea',
    width: 140,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '自用面积(㎡)',
    dataIndex: 'useArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '出租面积(㎡)',
    dataIndex: 'rentArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '出借面积(㎡)',
    dataIndex: 'lendArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '占用面积(㎡)',
    dataIndex: 'occupyArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '转让面积(㎡)',
    dataIndex: 'sellArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '其他面积(㎡)',
    dataIndex: 'otherArea',
    width: 120,
    align: 'right',
    customRender: ({ text }) => {
      return text ? Number(text).toFixed(2) : '';
    },
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
  },
  {
    title: '经办人',
    dataIndex: 'operator',
    width: 120,
  },
  {
    title: '录入人',
    dataIndex: 'entryClerk',
    width: 120,
  },
  {
    title: '录入时间',
    dataIndex: 'createTime',
    width: 160,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '资产项目（资产名称）',
    field: 'name',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产项目（资产名称）',
    },
  },
  {
    label: '资产编号',
    field: 'code',
    component: 'Input',
    componentProps: {
      placeholder: '请输入资产编号',
    },
  },
  {
    label: '所属企业',
    field: 'companyName',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择所属企业',
      api: getUserCompany,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    label: '状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择状态',
      dictCode: 'record_status',
    },
  },
  {
    label: '企业自定义编号',
    field: 'enterpriseCode',
    component: 'Input',
    componentProps: {
      placeholder: '请输入企业自定义编号',
    },
  },
  {
    label: '权属单位名称',
    field: 'ownUnit',
    component: 'Input',
    componentProps: {
      placeholder: '请输入权属单位名称',
    },
  },
  {
    label: '管理单位',
    field: 'manageUnit',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择管理单位',
      api: getCompanyHandle,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    label: '资产位置',
    field: 'region',
    component: 'JAreaLinkage',
    componentProps: {
      placeholder: '请选择省/市/区',
      showArea: true,
      showAll: false,
      saveCode: 'all',
    },
  },
  {
    label: '详细地址',
    field: 'address',
    component: 'Input',
    componentProps: {
      placeholder: '请输入详细地址',
    },
  },
  {
    label: '房屋用途',
    field: 'houseTypes',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择房屋用途',
      mode: 'multiple',
      dictCode: 'house_types',
    },
  },
  {
    label: '资产入账日期',
    field: 'assetEntryDate',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '房屋性质',
    field: 'source',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择房屋性质',
      dictCode: 'house_property',
    },
  },
  {
    label: '是否有产权证',
    field: 'property',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'land_property_type',
    },
  },
  {
    label: '资产使用状态',
    field: 'assetsStatus',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      mode: 'multiple',
      dictCode: 'land_assets_status',
    },
  },
  // {
  //   label: '总面积区间(㎡)',
  //   field: 'totalAreaMin',
  //   component: 'InputNumber',
  //   componentProps: {
  //     placeholder: '最小值',
  //     min: 0,
  //     precision: 2,
  //     style: { width: '100%' },
  //   },
  // },
  {
    label: '总面积区间(㎡)',
    field: 'totalArea',
    component: 'JRangeNumber',
    componentProps: {
      min: 0,
      precision: 2,
      style: { width: '100%' },
    },
  },
  // {
  //   label: '总面积区间(㎡)',
  //   field: 'totalAreaMax',
  //   component: 'InputNumber',
  //   componentProps: {
  //     placeholder: '最大值',
  //     min: 0,
  //     precision: 2,
  //     style: { width: '100%' },
  //   },
  // },
  {
    label: '是否账外',
    field: 'offAccount',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    label: '是否危房',
    field: 'dangerousHouseOrNot',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    label: '是否报送国资委',
    field: 'reportOrNot',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    label: '是否竣工财务结算',
    field: 'completionOrNot',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    label: '是否拖欠工程款',
    field: 'owingOrNot',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    label: '是否具盘活价值',
    field: 'vitalizeOrNot',
    component: 'JDictSelectTag',
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    label: '经办人',
    field: 'operator',
    component: 'Input',
    componentProps: {
      placeholder: '请输入经办人',
    },
  },
  {
    label: '录入人',
    field: 'entryClerk',
    component: 'Input',
    componentProps: {
      placeholder: '请输入录入人',
    },
  },
  {
    label: '录入时间',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
];
