<template>
  <div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="handleExport"
          :disabled="selectedRowKeys.length === 0">导出</a-button>
        <a-button type="primary" preIcon="ant-design:download-outlined" @click="handleExportAll">全部导出</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>

    <!-- 二维码弹窗 -->
    <BasicModal v-bind="$attrs" @register="registerQRCodeModal" title="资产二维码" :width="500" :showCancelBtn="false" :showOkBtn="false">
      <div class="qrcode-dialog-content">
        <div class="qrcode-title">{{ currentAsset.name }}</div>
        <div class="qrcode-image-container">
          <QrCode :value="qrCodeContent" :width="200" :height="200" />
        </div>
        <div class="qrcode-info">
          <div class="qrcode-info-item">
            <span class="qrcode-info-label">资产编号：</span>
            <span>{{ currentAsset.code }}</span>
          </div>
          <div class="qrcode-info-item">
            <span class="qrcode-info-label">资产类型：</span>
            <span>{{ formatAssetType(currentAsset.assetType) }}</span>
          </div>
          <div class="qrcode-info-item">
            <span class="qrcode-info-label">所属企业：</span>
            <span>{{ formatCompanyName(currentAsset.companyName) }}</span>
          </div>
        </div>
        <div class="qrcode-action-buttons">
          <a-button size="small" type="primary" @click="copyQRCode">
            <template #icon><CopyOutlined /></template>
            复制二维码
          </a-button>
          <a-button size="small" type="primary" @click="downloadQRCode">
            <template #icon><DownloadOutlined /></template>
            下载二维码
          </a-button>
        </div>
      </div>
    </BasicModal>
  </div>
</template>

<script lang="ts" name="AssetsQrcodeList" setup>
  import { unref } from 'vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { QrCode } from '/@/components/Qrcode/index';
  import { columns, searchFormSchema } from './qrcode.data';
  import { list, exportExcel } from './qrcode.api';
  import { CopyOutlined, DownloadOutlined } from '@ant-design/icons-vue';
  import { ref } from 'vue';
  import { useMethods } from '/@/hooks/system/useMethods';
  import { useCopyToClipboard } from '/@/hooks/web/useCopyToClipboard';

  const { createMessage } = useMessage();
  const { handleExportXls } = useMethods();
  const { clipboardRef, isSuccessRef } = useCopyToClipboard();

  const exportLoading = ref(false);

  // 二维码弹窗
  const [registerQRCodeModal, { openModal: openQRCodeModal }] = useModal();

  // 当前选中的资产信息
  const currentAsset = ref({});
  const qrCodeContent = ref('');
  const selectedRowKeys = ref<any[]>([]);

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'assets-qrcode-list',
    tableProps: {
      title: '资产二维码列表',
      api: list,
      columns: columns,
      size: 'small',
      canResize: false,
      // 显示序号列
      showIndexColumn: true,
      // 显示表格设置
      showTableSetting: true,
      // 表格设置配置
      tableSetting: {
        // 缓存键名
        cacheKey: 'assets_qrcode_list',
        // 显示列设置
        setting: true,
        // 显示刷新按钮
        redo: false,
        // 显示尺寸调整
        size: false,
        // 显示全屏按钮
        fullScreen: false,
      },
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 140,
        // 显示展开/收起按钮
        showAdvancedButton: true,
        // 超过3列时默认折叠
        autoAdvancedCol: 4,
        baseColProps: {
          xs: 24,
          sm: 12,
          md: 8,
          lg: 6,
          xl: 6,
          xxl: 6,
        },
      },
      actionColumn: {
        width: 100,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        if (params.assetsAmount) {
          const [min, max] = params.assetsAmount.split(',');
          params.assetsAmountMin = min;
          params.assetsAmountMax = max;
        }

        if (params.assetsLocation) {
          console.log(params.assetsLocation, 'params.assetsLocation');
          const [province, city, area] = params.assetsLocation.split(',');
          params.province = province;
          params.city = city;
          params.area = area;
        }
        console.log(params, 'params');
        return params;
      },
    },
  });

  const [registerTable, { reload, getForm }] = tableContext;

  // 表格行选择配置
  const rowSelection = {
    type: 'checkbox',
    onChange: (keys: any[], rows: any[]) => {
      selectedRowKeys.value = keys;
    },
  };

  // 获取表格操作按钮
  const getTableAction = (record): ActionItem[] => {
    return [
      {
        label: '二维码',
        icon: 'ant-design:qrcode-outlined',
        onClick: () => handleShowQRCode(record),
      },
    ];
  };

  // 显示二维码
  const handleShowQRCode = (record) => {
    currentAsset.value = { ...record };
    // 生成二维码内容
    qrCodeContent.value = JSON.stringify({
      id: record.id,
      code: record.code,
      type: record.assetType,
      name: record.name,
    });
    openQRCodeModal();
  };

  // 导出所选数据
  const handleExport = async () => {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请至少选择一条数据');
      return;
    }
    const formData = getForm().getFieldsValue();
    formData.ids = selectedRowKeys.value;
    exportLoading.value = true;
    await handleExportXls('资产二维码', exportExcel, formData, 'post')
      .then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  };

  // 导出全部数据
  const handleExportAll = async () => {
    const formData = getForm().getFieldsValue();
    formData.ids = selectedRowKeys.value;
    exportLoading.value = true;
    await handleExportXls('资产二维码', exportExcel, formData, 'post')
      .then((res) => {
        console.log(res, 'res');
      })
      .finally(() => {
        exportLoading.value = false;
      });
  };

  // 复制二维码
  const copyQRCode = async () => {
    try {
      const canvas = document.querySelector('.qrcode-image-container canvas') as HTMLCanvasElement;
      if (!canvas) {
        createMessage.error('未找到二维码图片');
        return;
      }

      // 将canvas转换为blob
      canvas.toBlob(async (blob) => {
        if (!blob) {
          createMessage.error('二维码图片转换失败');
          return;
        }

        try {
          // 使用现代的 Clipboard API 复制图片
          if (navigator.clipboard && window.ClipboardItem) {
            const item = new ClipboardItem({ 'image/png': blob });
            await navigator.clipboard.write([item]);
            createMessage.success('二维码已复制到剪贴板');
          } else {
            // 降级方案：复制二维码的数据URL
            const dataUrl = canvas.toDataURL();
            clipboardRef.value = dataUrl;
            if (unref(isSuccessRef)) {
              createMessage.success('二维码数据已复制到剪贴板');
            } else {
              createMessage.error('复制失败，请手动保存二维码');
            }
          }
        } catch (error) {
          console.error('复制二维码失败:', error);
          createMessage.error('复制失败，请手动保存二维码');
        }
      }, 'image/png');
    } catch (error) {
      console.error('复制二维码失败:', error);
      createMessage.error('复制失败，请手动保存二维码');
    }
  };

  // 下载二维码
  const downloadQRCode = () => {
    try {
      const canvas = document.querySelector('.qrcode-image-container canvas') as HTMLCanvasElement;
      if (!canvas) {
        createMessage.error('未找到二维码图片');
        return;
      }

      const asset = unref(currentAsset) as any;
      const fileName = `qrcode_${asset?.code || asset?.name || 'asset'}.png`;

      // 创建下载链接
      const link = document.createElement('a');
      link.download = fileName;
      link.href = canvas.toDataURL('image/png');

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      createMessage.success('二维码下载成功');
    } catch (error) {
      console.error('下载二维码失败:', error);
      createMessage.error('下载失败，请重试');
    }
  };

  // 格式化资产类型
  const formatAssetType = (type) => {
    const typeMap = {
      0: '土地',
      1: '房屋',
      2: '广告位',
      3: '设备',
      4: '其他',
    };
    return typeMap[type] || '未知';
  };

  // 格式化企业名称
  const formatCompanyName = (value) => {
    const companyMap = {
      0: '厦门市城市建设发展投资有限公司',
      1: '厦门市地热资源管理有限公司',
      2: '厦门兴地房屋征迁服务有限公司',
      3: '厦门地丰置业有限公司',
      4: '图智策划咨询（厦门）有限公司',
      5: '厦门市集众祥和物业管理有限公司',
      6: '厦门市人居乐业物业服务有限公司',
    };
    return companyMap[value] || '未知';
  };
</script>

<style lang="less" scoped>
.qrcode-dialog-content {
  text-align: center;
  padding: 20px 0;

  .qrcode-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
  }

  .qrcode-image-container {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
  }

  .qrcode-info {
    margin-bottom: 15px;
    color: rgba(0, 0, 0, 0.65);
    text-align: left;
    padding: 0 20px;

    .qrcode-info-item {
      margin-bottom: 8px;
      display: flex;

      .qrcode-info-label {
        width: 100px;
        color: #606266;
      }
    }
  }

  .qrcode-action-buttons {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    gap: 10px;
  }
}
</style> 