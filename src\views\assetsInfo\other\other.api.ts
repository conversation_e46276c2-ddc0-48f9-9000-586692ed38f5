import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';

enum Api {
  list = '/biz/other/queryPage',
  save = '/biz/other/add',
  edit = '/biz/other/edit',
  delete = '/biz/other/delete',
  deleteBatch = '/mock/other/deleteBatch',
  importExcel = '/biz/other/excel/import',
  exportXls = '/biz/other/excel/export',
  exportAll = '/mock/other/exportAll',
  downloadTemplate = '/biz/other/downloadImportTemplate',
  detail = '/biz/other/detail/',
  sum = 'biz/other/querySum',
}

export const exportExcel = Api.exportXls;
export const downloadTemplate = Api.downloadTemplate;
export const importExcel = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.post({ url: Api.list, params });

/**
 * 保存或更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  if (isUpdate) {
    return defHttp.put({ url: Api.edit, params });
  } else {
    return defHttp.post({ url: Api.save, params });
  }
};

/**
 * 获取详情
 * @param id
 */
export const getDetail = (id) => defHttp.get({ url: Api.detail + id, params: { id } });

/**
 * 删除
 * @param params
 * @param handleSuccess
 */
export const deleteOther = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.delete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDeleteOther = (params, handleSuccess) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

export const getSum = (params) => defHttp.post({ url: Api.sum, params });
