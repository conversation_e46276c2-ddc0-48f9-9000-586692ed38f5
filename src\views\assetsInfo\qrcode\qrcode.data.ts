import { BasicColumn } from '/@/components/Table';
import { h } from 'vue';
import { FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { getTextByCode } from '/@/components/Form/src/utils/areaDataUtil';
import { getCompanyHandle, getUserCompany } from '/@/api/common/api';

export const columns: BasicColumn[] = [
  {
    title: '资产编号',
    dataIndex: 'code',
    width: 120,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '企业自定义编号',
    dataIndex: 'enterpriseCode',
    width: 150,
    ellipsis: true,
  },
  {
    title: '资产名称',
    dataIndex: 'name',
    width: 160,
    ellipsis: true,
  },
  {
    title: '资产类型',
    dataIndex: 'assetType',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(text, 'assets_type');
    },
  },
  {
    title: '所属集团',
    dataIndex: 'groupName',
    width: 160,
    ellipsis: true,
    customRender: ({ text }) => {
      return render.renderDict(text, 'group_name');
    },
  },
  {
    title: '所属企业',
    dataIndex: 'companyNameString',
    width: 160,
    ellipsis: true,
  },
  {
    title: '管理单位',
    dataIndex: 'manageUnitName',
    width: 160,
    ellipsis: true,
  },
  {
    title: '报送国资委',
    dataIndex: 'reportOrNot',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDict(`${text}`, 'yes_no');
    },
  },
  {
    title: '省份',
    dataIndex: 'province',
    width: 100,
    ellipsis: true,
    customRender: ({ record }) => {
      return getTextByCode(record.province);
    },
  },
  {
    title: '城市',
    dataIndex: 'city',
    width: 100,
    ellipsis: true,
    customRender: ({ record }) => {
      return getTextByCode(record.province);
    },
  },
  {
    title: '区县',
    dataIndex: 'area',
    width: 100,
    ellipsis: true,
    customRender: ({ record }) => {
      return getTextByCode(record.province);
    },
  },
  {
    title: '详细地址',
    dataIndex: 'address',
    width: 200,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(text, 'record_status');
    },
  },
  {
    title: '资产使用状态',
    dataIndex: 'assetsStatus',
    width: 150,
    ellipsis: true,
    customRender: ({ text }) => {
      const textArray = text ? text.split(',') : [];
      return h(
        'div',
        textArray.map((item) => {
          const finalText = render.renderDict(item, 'land_assets_status', true);
          return h('span', finalText);
        })
      );
    },
  },
  {
    title: '资产取得日期',
    dataIndex: 'gainDate',
    width: 120,
  },
  {
    title: '资产入账日期',
    dataIndex: 'assetEntryDate',
    width: 120,
  },
  {
    title: '资产原值(元)',
    dataIndex: 'assetsAmount',
    width: 150,
    customRender: ({ text }) => {
      return text ? text.toFixed(2) : '';
    },
  },
  {
    title: '账面价值(元)',
    dataIndex: 'bookAmount',
    width: 150,
    customRender: ({ text }) => {
      return text ? text.toFixed(2) : '';
    },
  },
  {
    title: '账面价值时点',
    dataIndex: 'dateOfBookValue',
    width: 150,
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    ellipsis: true,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'name',
    label: '资产名称',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入资产名称',
    },
  },
  {
    field: 'assetTypes',
    label: '资产类型',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      mode: 'multiple',
      placeholder: '请选择资产类型',
      dictCode: 'assets_type',
    },
  },
  {
    field: 'companyName',
    label: '所属企业',
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择所属企业',
      api: getUserCompany,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择状态',
      dictCode: 'record_status',
    },
  },
  {
    field: 'code',
    label: '资产编号',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入资产编号',
    },
  },
  {
    field: 'enterpriseCode',
    label: '企业自定义编号',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入企业自定义编号',
    },
  },
  {
    field: 'assetsLocation',
    label: '资产位置',
    component: 'JAreaLinkage',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择省/市/区',
      showArea: true,
      saveCode: 'all',
    },
  },
  {
    field: 'manageUnit',
    label: '管理单位',
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择管理单位',
      api: getCompanyHandle,
      transResult: (originResult) => {
        return originResult.map((item) => ({ value: item.id, label: item.name }));
      },
    },
  },
  {
    field: 'reportOrNot',
    label: '是否报送国资委',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请选择',
      dictCode: 'yes_no',
    },
  },
  {
    field: 'address',
    label: '详细地址',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入详细地址',
    },
  },
  {
    field: 'assetsStatus',
    label: '资产使用状态',
    component: 'JDictSelectTag',
    colProps: { span: 6 },
    componentProps: {
      mode: 'multiple',
      placeholder: '请选择资产使用状态',
      dictCode: 'land_assets_status',
    },
  },
  {
    field: 'gainDate',
    label: '资产取得日期',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'assetEntryDate',
    label: '资产入账日期',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['开始日期', '结束日期'],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    field: 'assetsAmount',
    label: '资产原值区间(元)',
    component: 'JRangeNumber',
    colProps: { span: 6 },
    componentProps: {
      placeholder: ['最小值', '最大值'],
      precision: 2,
      min: 0,
      style: { width: '100%' },
    },
  },
];
