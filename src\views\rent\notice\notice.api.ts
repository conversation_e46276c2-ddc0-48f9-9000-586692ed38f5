import { defHttp } from '/@/utils/http/axios';

enum Api {
  NoticeList = '/rent/notice/queryPageList',
  NoticeDetail = '/rent/notice/queryById',
  NoticeAdd = '/rent/notice/add',
  NoticeUpdate = '/rent/notice/edit',
  NoticeDelete = '/rent/notice/delete',
  LinkAssets = '/rent/notice/association',

  // 获取资产包选项
  getAssetPackageInfo = '/rent/notice/getAssetPackageInfo/',

  getRentInfoAssetPackageInfo = '/biz/rentInfo/getAssetPackageInfo/',
}

/**
 * 获取招租公告列表
 * @param params 查询参数
 */
export const getNoticeList = (params) => {
  return defHttp.post({
    url: Api.NoticeList,
    params,
  });
};

/**
 * 获取招租公告详情
 * @param id 公告ID
 */
export const getNoticeDetail = (id: string | number) => {
  return defHttp.get({
    url: Api.NoticeDetail,
    params: { id },
  });
};

/**
 * 新增招租公告
 * @param params 公告数据
 */
export const addNotice = (params) => {
  return defHttp.post({
    url: Api.NoticeAdd,
    params,
  });
};

/**
 * 更新招租公告
 * @param params 公告数据
 */
export const updateNotice = (params) => {
  return defHttp.put({
    url: Api.NoticeUpdate,
    params,
  });
};

/**
 * 删除招租公告
 * @param id 公告ID
 */
export const deleteNotice = (id: string | number) => {
  return defHttp.delete({
    url: Api.NoticeDelete + `?id=${id}`,
  });
};

/**
 * 关联资产包
 * @param params 关联参数
 */
export const linkAssets = (params) => {
  return defHttp.post({
    url: Api.LinkAssets,
    params,
  });
};

/**
 * 获取资产包选项
 * @param params 查询参数
 */
export const getAssetPackageInfo = (params) => {
  return defHttp.get({
    url: Api.getAssetPackageInfo,
    params,
  });
};

export const getRentInfoAssetPackageInfo = (params) => {
  return defHttp.get({
    url: Api.getRentInfoAssetPackageInfo,
    params,
  });
};
