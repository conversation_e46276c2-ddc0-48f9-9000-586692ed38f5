<template>
  <div class="other-assets-form">
    <div class="p-4">
      <a-form ref="formRef" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- 基本信息 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:info-circle-outlined" class="title-icon" />
              基本信息
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="资产编号" name="code">
                  <a-input v-model:value="formData.code" placeholder="保存后系统自动生成" disabled />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="企业自定义编号" name="enterpriseCode">
                  <a-input v-model:value="formData.enterpriseCode" placeholder="请输入企业自定义编号" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="name" :rules="[{ required: true, message: '请输入资产名称', trigger: 'blur' }]">
                  <template #label>
                    资产名称
                    <a-tooltip title="系统内要求资产名称唯一">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-input v-model:value="formData.name" placeholder="请输入资产名称" />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="所属集团" name="groupName">
                  <JDictSelectTag
                    v-model:value="formData.groupName"
                    :showChooseOption="false"
                    placeholder="请选择所属集团"
                    disabled
                    dictCode="group_name"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="所属企业" name="companyName" :rules="[{ required: true, message: '请选择所属企业', trigger: 'change' }]">
                  <ApiSelect
                    v-model:value="formData.companyName"
                    placeholder="请选择所属企业"
                    :api="getUserCompany"
                    :transResult="(originResult) => originResult.map((item) => ({ value: item.id, label: item.name }))"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="资产位置" name="assetsLocation" :rules="[{ required: true, message: '请选择省/市/区', trigger: 'change' }]">
                  <JAreaLinkage
                    v-model:value="formData.assetsLocation"
                    placeholder="请选择省/市/区"
                    :showArea="true"
                    :showAll="false"
                    saveCode="all"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="详细地址" name="address">
                  <a-input v-model:value="formData.address" placeholder="请输入详细地址" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="status" :rules="[{ required: true, message: '请选择状态', trigger: 'change' }]">
                  <template #label>
                    状态
                    <a-tooltip title="备案数据支持撤回、草稿数据和撤回数据支持作废">
                      <QuestionCircleOutlined />
                    </a-tooltip>
                  </template>
                  <a-select v-model:value="formData.status" placeholder="请选择状态">
                    <a-select-option value="0" :disabled="initStatus === '1' || initStatus === '2' || initStatus === '4'">草稿</a-select-option>
                    <a-select-option value="1" :disabled="initStatus === '2' || initStatus === '4'">备案</a-select-option>
                    <a-select-option value="2" :disabled="initStatus === '-1' || initStatus === '0' || initStatus === '4'">撤回</a-select-option>
                    <a-select-option value="4" :disabled="initStatus === '-1' || initStatus === '0' || initStatus === '1'">作废</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="管理单位" name="manageUnit" :rules="[{ required: true, message: '请选择管理单位', trigger: 'change' }]">
                  <ApiSelect
                    v-model:value="formData.manageUnit"
                    placeholder="请选择管理单位"
                    :api="getCompanyHandle"
                    :transResult="(originResult) => originResult.map((item) => ({ value: item.id, label: item.name }))"
                  />
                  <div class="help-text">将使用管理单位作为数据权限判断依据</div>
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="是否报送国资委" name="reportOrNot" :rules="[{ required: true, message: '请选择', trigger: 'change' }]">
                  <JDictSelectTag v-model:value="formData.reportOrNot" :showChooseOption="false" dictCode="yes_no" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="经办人" name="operator" :rules="[{ required: true, message: '请输入经办人', trigger: 'blur' }]">
                  <a-input v-model:value="formData.operator" placeholder="请输入经办人" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="录入人" name="entryClerk" :rules="[{ required: true, message: '请输入录入人', trigger: 'blur' }]">
                  <a-input v-model:value="formData.entryClerk" disabled />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="录入时间" name="createTime" :rules="[{ required: true, message: '请输入录入时间', trigger: 'blur' }]">
                  <a-input v-model:value="formData.createTime" disabled />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 资产情况 -->
        <div class="form-card">
          <div class="form-card-header">
            <div class="form-card-title">
              <Icon icon="ant-design:building-outlined" class="title-icon" />
              资产情况
            </div>
          </div>
          <div class="form-card-body">
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="资产使用状态" name="assetsStatus" :rules="[{ required: true, message: '请选择资产使用状态', trigger: 'change' }]">
                  <!-- <a-select v-model:value="formData.assetsStatus" mode="multiple" placeholder="请选择资产使用状态" style="width: 100%">
                    <a-select-option value="0">闲置</a-select-option>
                    <a-select-option value="1">自用</a-select-option>
                    <a-select-option value="2">出租</a-select-option>
                    <a-select-option value="3">出借</a-select-option>
                    <a-select-option value="4">占用</a-select-option>
                    <a-select-option value="5">欠租</a-select-option>
                    <a-select-option value="6">转让</a-select-option>
                    <a-select-option value="7">其他</a-select-option>
                  </a-select> -->
                  <JDictSelectTag v-model:value="formData.assetsStatus" mode="multiple" :showChooseOption="false" dictCode="land_assets_status" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="资产取得日期" name="gainDate">
                  <a-date-picker
                    v-model:value="formData.gainDate"
                    placeholder="请选择资产取得日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="资产入账日期" name="assetEntryDate">
                  <a-date-picker
                    v-model:value="formData.assetEntryDate"
                    placeholder="请选择资产入账日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="8">
                <a-form-item label="资产原值（元）" name="assetsAmount" :rules="[{ required: true, message: '请输入资产原值', trigger: 'blur' }]">
                  <a-input-number
                    v-model:value="formData.assetsAmount"
                    placeholder="请输入资产原值"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="账面价值（元）" name="bookAmount">
                  <a-input-number
                    v-model:value="formData.bookAmount"
                    placeholder="请输入账面价值"
                    :precision="2"
                    :min="0"
                    :controls="false"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="账面价值时点" name="dateOfBookValue">
                  <a-date-picker
                    v-model:value="formData.dateOfBookValue"
                    placeholder="请选择账面价值时点"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :disabled-date="disabledDate"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="20">
              <a-col :span="24">
                <a-form-item label="备注" name="remark" :labelCol="{ span: 2 }">
                  <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="4" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 表单操作按钮 -->
        <div class="form-footer">
          <a-button type="primary" @click="handleSubmit" :loading="loading"> 提交 </a-button>
          <a-button @click="handleReset" style="margin-left: 12px"> 重置 </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" name="OtherAssetsForm" setup>
  import { ref, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useUserStore } from '/@/store/modules/user';
  import { Icon } from '/@/components/Icon';
  import { JDictSelectTag, JAreaLinkage, ApiSelect } from '/@/components/Form';
  import { QuestionCircleOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getCompanyHandle, getUserCompany } from '/@/api/common/api';
  import { saveOrUpdate, getDetail } from './other.api';
  import dayjs from 'dayjs';

  const user = useUserStore().getUserInfo;
  const route = useRoute();
  const router = useRouter();
  const { createMessage, createConfirm } = useMessage();
  const loading = ref(false);

  // 获取路由参数判断是新增还是编辑
  const isUpdate = ref(false);
  const recordId = ref('');
  const initStatus = ref('-1');

  // 表单引用
  const formRef = ref();

  // 表单数据
  const formData = ref<any>({
    // 基本信息
    id: '',
    code: '',
    enterpriseCode: '',
    name: '',
    groupName: '0',
    companyName: '',
    assetsLocation: '',
    address: '',
    status: '0',
    manageUnit: '',
    reportOrNot: '',
    operator: '',
    entryClerk: '',
    createTime: '',
    // 资产情况
    assetsStatus: [],
    gainDate: '',
    assetEntryDate: '',
    assetsAmount: 0,
    bookAmount: 0,
    dateOfBookValue: '',
    remark: '',
  });

  // 日期禁用函数
  const disabledDate = (current: dayjs.Dayjs) => {
    return current && current > dayjs().endOf('day');
  };

  // 初始化
  onMounted(async () => {
    // 判断是新增还是编辑
    const { id } = route.params;
    if (id) {
      isUpdate.value = true;
      recordId.value = id as string;
      await loadDetail();
    } else {
      isUpdate.value = false;
      initStatus.value = '-1';
      // 设置默认值
      Object.assign(formData.value, {
        groupName: '0',
        status: '0',
        entryClerk: user.realname || '当前用户',
        createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      });
    }
  });

  // 加载详情
  async function loadDetail() {
    try {
      const data = await getDetail(recordId.value);
      if (data) {
        Object.assign(formData.value, {
          ...data,
          status: `${data.status}`,
          reportOrNot: `${data.reportOrNot}`,
          assetsLocation: `${data.province},${data.city},${data.area}`,
          assetsStatus: data.assetsStatus ? data.assetsStatus.split(',') : [],
        });
        initStatus.value = `${data.status}`;
      }
    } catch (error) {
      createMessage.error('加载详情失败');
    }
  }

  // 提交表单
  async function handleSubmit() {
    try {
      // 验证表单
      await formRef.value.validate();

      // 合并表单数据
      const submitData = {
        ...formData.value,
        assetsStatus: formData.value.assetsStatus.join(','),
      };

      // 处理地区数据
      if (submitData.assetsLocation && Array.isArray(submitData.assetsLocation)) {
        const [province, city, area] = submitData.assetsLocation;
        submitData.province = province;
        submitData.city = city;
        submitData.area = area;
      }

      loading.value = true;
      await saveOrUpdate(submitData, isUpdate.value);

      createMessage.success(isUpdate.value ? '更新成功' : '新增成功');
      router.push('/assetsInfo/other');
    } catch (error) {
      console.log(error, 'error');
      createMessage.error(isUpdate.value ? '更新失败' : '新增失败');
    } finally {
      loading.value = false;
    }
  }

  // 重置表单
  function handleReset() {
    createConfirm({
      title: '确认重置',
      content: '是否确认重置表单数据？',
      iconType: 'warning',
      onOk: () => {
        if (isUpdate.value) {
          loadDetail();
        } else {
          // 重置表单数据
          Object.assign(formData.value, {
            id: '',
            code: '',
            enterpriseCode: '',
            name: '',
            groupName: '0',
            companyName: '',
            assetsLocation: '',
            address: '',
            status: '0',
            manageUnit: '',
            reportOrNot: '',
            operator: '',
            entryClerk: user.realname || '当前用户',
            createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            assetsStatus: [],
            gainDate: '',
            assetEntryDate: '',
            assetsAmount: 0,
            bookAmount: 0,
            dateOfBookValue: '',
            remark: '',
          });
        }
      },
    });
  }
</script>

<style lang="less" scoped>
  .other-assets-form {
    .simple-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
      padding: 16px 24px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
    }

    .form-card {
      background: white;
      border-radius: 8px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .form-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 24px;
        border-bottom: 1px solid #f0f0f0;

        .form-card-title {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-weight: 500;
          color: #333;

          .title-icon {
            margin-right: 8px;
            color: #1890ff;
          }
        }
      }

      .form-card-body {
        padding: 24px;
      }
    }

    .form-footer {
      text-align: center;
      padding: 24px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .help-text {
      font-size: 12px;
      color: #909399;
      line-height: 1.5;
      margin-top: 5px;
    }

    // 统一设置表单项标签宽度
    :deep(.ant-form-item-label) {
      width: 180px;
      min-width: 180px;
      text-align: right;
      padding-right: 8px;
    }

    :deep(.ant-form-item-label > label) {
      width: 100%;
      justify-content: flex-end;
    }
  }
</style>